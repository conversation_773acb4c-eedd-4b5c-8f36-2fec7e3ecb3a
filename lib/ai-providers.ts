// AI服务提供商统一接口
export interface AIResponse {
  content: string
  usage?: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

export interface AIMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

export interface AIRequestOptions {
  model?: string
  temperature?: number
  max_tokens?: number
  stream?: boolean
}

// AI服务提供商抽象基类
export abstract class AIProvider {
  protected apiKey: string
  protected apiUrl: string
  protected defaultModel: string

  constructor(apiKey: string, apiUrl: string, defaultModel: string) {
    this.apiKey = apiKey
    this.apiUrl = apiUrl
    this.defaultModel = defaultModel
  }

  abstract callAPI(
    messages: AIMessage[],
    options?: AIRequestOptions
  ): Promise<AIResponse>

  abstract validateResponse(response: any): boolean
  abstract extractContent(response: any): string
  abstract extractUsage(response: any): AIResponse['usage']
}

// GLM-4.5-air 提供商实现
export class GLMProvider extends AIProvider {
  constructor(
    apiKey: string = process.env.GLM_API_KEY || '',
    apiUrl: string = process.env.GLM_API_URL || 'https://open.bigmodel.cn/api/paas/v4/chat/completions',
    defaultModel: string = process.env.GLM_MODEL || 'glm-4.5-air'
  ) {
    super(apiKey, apiUrl, defaultModel)
    if (!this.apiKey) {
      throw new Error('GLM_API_KEY environment variable is required')
    }
  }

  async callAPI(messages: AIMessage[], options: AIRequestOptions = {}): Promise<AIResponse> {
    const requestBody = {
      model: options.model || this.defaultModel,
      messages: messages,
      temperature: options.temperature ?? 0.3,
      max_tokens: options.max_tokens ?? 1000,
      stream: options.stream ?? false
    }

    const response = await fetch(this.apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': this.apiKey,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`GLM API error: ${response.status} - ${errorText}`)
    }

    const data = await response.json()
    
    if (!this.validateResponse(data)) {
      throw new Error('Invalid GLM API response format')
    }

    return {
      content: this.extractContent(data),
      usage: this.extractUsage(data)
    }
  }

  validateResponse(response: any): boolean {
    return response && 
           response.choices && 
           Array.isArray(response.choices) && 
           response.choices.length > 0 &&
           response.choices[0].message &&
           typeof response.choices[0].message.content === 'string'
  }

  extractContent(response: any): string {
    return response.choices[0].message.content
  }

  extractUsage(response: any): AIResponse['usage'] {
    if (response.usage) {
      return {
        prompt_tokens: response.usage.prompt_tokens || 0,
        completion_tokens: response.usage.completion_tokens || 0,
        total_tokens: response.usage.total_tokens || 0
      }
    }
    return undefined
  }
}

// DeepSeek 提供商实现
export class DeepSeekProvider extends AIProvider {
  constructor(
    apiKey: string = process.env.DEEPSEEK_API_KEY || '',
    apiUrl: string = process.env.DEEPSEEK_API_URL || 'https://api.deepseek.com/chat/completions',
    defaultModel: string = process.env.DEEPSEEK_MODEL || 'deepseek-chat'
  ) {
    super(apiKey, apiUrl, defaultModel)
    if (!this.apiKey) {
      throw new Error('DEEPSEEK_API_KEY environment variable is required')
    }
  }

  async callAPI(messages: AIMessage[], options: AIRequestOptions = {}): Promise<AIResponse> {
    const requestBody = {
      model: options.model || this.defaultModel,
      messages: messages,
      temperature: options.temperature ?? 0.3,
      max_tokens: options.max_tokens ?? 1000,
      stream: options.stream ?? false
    }

    const response = await fetch(this.apiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`DeepSeek API error: ${response.status} - ${errorText}`)
    }

    const data = await response.json()
    
    if (!this.validateResponse(data)) {
      throw new Error('Invalid DeepSeek API response format')
    }

    return {
      content: this.extractContent(data),
      usage: this.extractUsage(data)
    }
  }

  validateResponse(response: any): boolean {
    return response && 
           response.choices && 
           Array.isArray(response.choices) && 
           response.choices.length > 0 &&
           response.choices[0].message &&
           typeof response.choices[0].message.content === 'string'
  }

  extractContent(response: any): string {
    return response.choices[0].message.content
  }

  extractUsage(response: any): AIResponse['usage'] {
    if (response.usage) {
      return {
        prompt_tokens: response.usage.prompt_tokens || 0,
        completion_tokens: response.usage.completion_tokens || 0,
        total_tokens: response.usage.total_tokens || 0
      }
    }
    return undefined
  }
}

// AI服务工厂
export class AIServiceFactory {
  private static instance: AIProvider | null = null

  static getProvider(): AIProvider {
    if (!this.instance) {
      const provider = process.env.AI_PROVIDER || 'glm'
      
      switch (provider.toLowerCase()) {
        case 'glm':
          this.instance = new GLMProvider()
          break
        case 'deepseek':
          this.instance = new DeepSeekProvider()
          break
        default:
          console.warn(`Unknown AI provider: ${provider}, falling back to GLM`)
          this.instance = new GLMProvider()
      }
    }
    
    return this.instance
  }

  static setProvider(provider: AIProvider): void {
    this.instance = provider
  }

  static resetProvider(): void {
    this.instance = null
  }

  static getCurrentProviderType(): string {
    const provider = process.env.AI_PROVIDER || 'glm'
    return provider.toLowerCase()
  }
}
