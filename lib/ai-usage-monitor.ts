import { supabase } from './supabase'

// AI 使用监控相关类型定义
export interface AIUsageLog {
  id?: number
  api_type: 'deep-search' | 'global-search' | 'analyze-url' | 'add-tool'
  model: string
  input_tokens: number
  output_tokens: number
  total_tokens: number
  cost_yuan: number
  is_cache_hit: boolean
  is_discount_period: boolean
  response_time_ms: number
  success: boolean
  error_message?: string
  request_id?: string
  user_query?: string
  created_at?: string
}

export interface AIUsageDailySummary {
  date_only: string
  total_calls: number
  successful_calls: number
  failed_calls: number
  success_rate: number
  total_input_tokens: number
  total_output_tokens: number
  total_tokens: number
  total_cost_yuan: number
  cache_hit_cost_yuan: number
  discount_cost_yuan: number
  avg_response_time_ms: number
  max_response_time_ms: number
  api_type_stats: Record<string, any>
}

export interface AIUsageMonthlySummary {
  year_month: string
  total_calls: number
  successful_calls: number
  failed_calls: number
  success_rate: number
  total_input_tokens: number
  total_output_tokens: number
  total_tokens: number
  total_cost_yuan: number
  avg_daily_cost_yuan: number
  daily_cost_distribution: Record<string, number>
  api_type_stats: Record<string, any>
}

// AI 服务价格配置（每百万 tokens）
export const AI_PRICING = {
  // GLM-4.5-air 价格配置（2024年最新价格）
  'glm-4.5-air': {
    standard: {
      input: 0.5,      // ¥0.5/百万tokens
      output: 1.5      // ¥1.5/百万tokens
    }
  },
  // DeepSeek 价格配置
  'deepseek-chat': {
    standard: {
      input_cache_hit: 0.5,      // 缓存命中
      input_cache_miss: 2.0,     // 缓存未命中
      output: 8.0
    },
    discount: {
      input_cache_hit: 0.25,     // 5折
      input_cache_miss: 1.0,     // 5折
      output: 4.0                // 5折
    }
  },
  'deepseek-reasoner': {
    standard: {
      input_cache_hit: 1.0,
      input_cache_miss: 4.0,
      output: 16.0
    },
    discount: {
      input_cache_hit: 0.25,     // 2.5折
      input_cache_miss: 1.0,     // 2.5折
      output: 4.0                // 2.5折
    }
  }
} as const

/**
 * AI 使用监控服务
 * 负责记录和统计 AI API 的使用情况和费用
 */
export class AIUsageMonitor {
  /**
   * 判断是否为优惠时段（北京时间 00:30-08:30）
   */
  private static isDiscountPeriod(): boolean {
    const now = new Date()
    // 转换为北京时间（UTC+8）
    const beijingTime = new Date(now.getTime() + (8 * 60 * 60 * 1000))
    const hour = beijingTime.getUTCHours()
    const minute = beijingTime.getUTCMinutes()
    
    // 00:30-08:30 为优惠时段
    return (hour === 0 && minute >= 30) || (hour >= 1 && hour < 8) || (hour === 8 && minute < 30)
  }

  /**
   * 计算 API 调用费用
   */
  private static calculateCost(
    inputTokens: number,
    outputTokens: number,
    model: string = 'glm-4.5-air',
    isCacheHit: boolean = false,
    isDiscountPeriod?: boolean
  ): number {
    const pricing = AI_PRICING[model as keyof typeof AI_PRICING]
    if (!pricing) {
      console.warn(`未知模型 ${model}，使用默认价格`)
      return 0
    }

    // GLM 模型的简单定价
    if (model.includes('glm')) {
      const rates = pricing.standard as any
      const inputCost = (inputTokens / 1000000) * rates.input
      const outputCost = (outputTokens / 1000000) * rates.output
      return Number((inputCost + outputCost).toFixed(4))
    }

    // DeepSeek 模型的复杂定价（保持原有逻辑）
    const isDiscount = isDiscountPeriod ?? this.isDiscountPeriod()
    const rates = isDiscount ? pricing.discount : pricing.standard

    // 计算输入 tokens 费用
    const inputRate = isCacheHit ? (rates as any).input_cache_hit : (rates as any).input_cache_miss
    const inputCost = (inputTokens / 1000000) * inputRate

    // 计算输出 tokens 费用
    const outputCost = (outputTokens / 1000000) * (rates as any).output

    return Number((inputCost + outputCost).toFixed(4))
  }

  /**
   * 记录 AI API 使用情况
   */
  static async logUsage(params: {
    apiType: AIUsageLog['api_type']
    model?: string
    inputTokens: number
    outputTokens: number
    responseTimeMs: number
    success: boolean
    errorMessage?: string
    requestId?: string
    userQuery?: string
    isCacheHit?: boolean
  }): Promise<void> {
    try {
      const {
        apiType,
        model = 'glm-4.5-air',
        inputTokens,
        outputTokens,
        responseTimeMs,
        success,
        errorMessage,
        requestId,
        userQuery,
        isCacheHit = false
      } = params

      const totalTokens = inputTokens + outputTokens
      const isDiscountPeriod = this.isDiscountPeriod()
      const cost = this.calculateCost(inputTokens, outputTokens, model, isCacheHit, isDiscountPeriod)

      const logData: Omit<AIUsageLog, 'id' | 'created_at'> = {
        api_type: apiType,
        model,
        input_tokens: inputTokens,
        output_tokens: outputTokens,
        total_tokens: totalTokens,
        cost_yuan: cost,
        is_cache_hit: isCacheHit,
        is_discount_period: isDiscountPeriod,
        response_time_ms: responseTimeMs,
        success,
        error_message: errorMessage,
        request_id: requestId,
        user_query: userQuery ? userQuery.substring(0, 500) : undefined // 限制长度，保护隐私
      }

      // 异步写入数据库，不阻塞主流程
      // 使用 Promise.resolve().then() 替代 setImmediate，兼容 Edge Runtime
      Promise.resolve().then(async () => {
        try {
          const { error } = await supabase
            .from('ai_usage_logs')
            .insert([logData])

          if (error) {
            console.error('记录 AI 使用日志失败:', error)
          } else {
            console.log(`AI 使用日志已记录: ${apiType}, tokens: ${totalTokens}, 费用: ¥${cost}`)
          }

          // 触发每日汇总更新
          await this.updateDailySummary()
        } catch (err) {
          console.error('AI 使用监控异步处理失败:', err)
        }
      })
    } catch (error) {
      console.error('记录 AI 使用情况失败:', error)
      // 不抛出错误，避免影响主业务流程
    }
  }

  /**
   * 更新每日汇总统计
   */
  private static async updateDailySummary(): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0]

      // 查询今日的使用统计
      const { data: todayLogs, error } = await supabase
        .from('ai_usage_logs')
        .select('*')
        .eq('date_only', today)

      if (error) {
        console.error('查询今日 AI 使用日志失败:', error)
        return
      }

      if (!todayLogs || todayLogs.length === 0) {
        return
      }

      // 计算汇总数据
      const summary = this.calculateDailySummary(todayLogs)

      // 更新或插入每日汇总
      const { error: upsertError } = await supabase
        .from('ai_usage_daily_summary')
        .upsert([{
          date_only: today,
          ...summary
        }], {
          onConflict: 'date_only'
        })

      if (upsertError) {
        console.error('更新每日汇总失败:', upsertError)
      }
    } catch (error) {
      console.error('更新每日汇总统计失败:', error)
    }
  }

  /**
   * 计算每日汇总数据
   */
  private static calculateDailySummary(logs: AIUsageLog[]): Omit<AIUsageDailySummary, 'date_only'> {
    const totalCalls = logs.length
    const successfulCalls = logs.filter(log => log.success).length
    const failedCalls = totalCalls - successfulCalls
    const successRate = totalCalls > 0 ? Number(((successfulCalls / totalCalls) * 100).toFixed(2)) : 0

    const totalInputTokens = logs.reduce((sum, log) => sum + log.input_tokens, 0)
    const totalOutputTokens = logs.reduce((sum, log) => sum + log.output_tokens, 0)
    const totalTokens = totalInputTokens + totalOutputTokens

    const totalCostYuan = logs.reduce((sum, log) => sum + log.cost_yuan, 0)
    const cacheHitCostYuan = logs.filter(log => log.is_cache_hit).reduce((sum, log) => sum + log.cost_yuan, 0)
    const discountCostYuan = logs.filter(log => log.is_discount_period).reduce((sum, log) => sum + log.cost_yuan, 0)

    const responseTimes = logs.map(log => log.response_time_ms)
    const avgResponseTimeMs = responseTimes.length > 0 ? Math.round(responseTimes.reduce((sum, time) => sum + time, 0) / responseTimes.length) : 0
    const maxResponseTimeMs = responseTimes.length > 0 ? Math.max(...responseTimes) : 0

    // 按 API 类型分组统计
    const apiTypeStats: Record<string, any> = {}
    logs.forEach(log => {
      if (!apiTypeStats[log.api_type]) {
        apiTypeStats[log.api_type] = {
          calls: 0,
          tokens: 0,
          cost: 0,
          success_rate: 0
        }
      }
      apiTypeStats[log.api_type].calls++
      apiTypeStats[log.api_type].tokens += log.total_tokens
      apiTypeStats[log.api_type].cost += log.cost_yuan
    })

    // 计算各 API 类型的成功率
    Object.keys(apiTypeStats).forEach(apiType => {
      const apiLogs = logs.filter(log => log.api_type === apiType)
      const successCount = apiLogs.filter(log => log.success).length
      apiTypeStats[apiType].success_rate = Number(((successCount / apiLogs.length) * 100).toFixed(2))
    })

    return {
      total_calls: totalCalls,
      successful_calls: successfulCalls,
      failed_calls: failedCalls,
      success_rate: successRate,
      total_input_tokens: totalInputTokens,
      total_output_tokens: totalOutputTokens,
      total_tokens: totalTokens,
      total_cost_yuan: Number(totalCostYuan.toFixed(4)),
      cache_hit_cost_yuan: Number(cacheHitCostYuan.toFixed(4)),
      discount_cost_yuan: Number(discountCostYuan.toFixed(4)),
      avg_response_time_ms: avgResponseTimeMs,
      max_response_time_ms: maxResponseTimeMs,
      api_type_stats: apiTypeStats
    }
  }

  /**
   * 获取今日使用统计（按AI提供商分别统计）
   */
  static async getTodayUsage(): Promise<any> {
    try {
      const today = new Date().toISOString().split('T')[0]

      // 获取今日详细使用记录，按模型分组统计
      const { data: logs, error: logsError } = await supabase
        .from('ai_usage_logs')
        .select('*')
        .gte('created_at', `${today}T00:00:00.000Z`)
        .lt('created_at', `${today}T23:59:59.999Z`)

      if (logsError) {
        console.error('获取今日使用日志失败:', logsError)
        return null
      }

      // 按模型分组统计
      const modelStats: { [key: string]: any } = {}
      // 按API类型分组统计
      const apiTypeStats: { [key: string]: any } = {}
      let totalCalls = 0
      let totalCost = 0
      let totalTokens = 0
      let totalInputTokens = 0
      let totalOutputTokens = 0
      let totalSuccessCalls = 0
      let totalResponseTime = 0
      let maxResponseTime = 0

      logs?.forEach(log => {
        const model = log.model || 'unknown'
        const apiType = log.api_type || 'unknown'

        // 模型统计
        if (!modelStats[model]) {
          modelStats[model] = {
            model,
            total_calls: 0,
            success_calls: 0,
            total_cost_yuan: 0,
            total_tokens: 0,
            input_tokens: 0,
            output_tokens: 0,
            avg_response_time_ms: 0,
            total_response_time: 0
          }
        }

        // API类型统计
        if (!apiTypeStats[apiType]) {
          apiTypeStats[apiType] = {
            calls: 0,
            cost: 0,
            tokens: 0
          }
        }

        // 更新模型统计
        modelStats[model].total_calls++
        if (log.success) modelStats[model].success_calls++
        modelStats[model].total_cost_yuan += log.cost_yuan || 0
        modelStats[model].total_tokens += log.total_tokens || 0
        modelStats[model].input_tokens += log.input_tokens || 0
        modelStats[model].output_tokens += log.output_tokens || 0
        modelStats[model].total_response_time += log.response_time_ms || 0

        // 更新API类型统计
        apiTypeStats[apiType].calls++
        apiTypeStats[apiType].cost += log.cost_yuan || 0
        apiTypeStats[apiType].tokens += log.total_tokens || 0

        // 更新总计
        totalCalls++
        if (log.success) totalSuccessCalls++
        totalCost += log.cost_yuan || 0
        totalTokens += log.total_tokens || 0
        totalInputTokens += log.input_tokens || 0
        totalOutputTokens += log.output_tokens || 0
        totalResponseTime += log.response_time_ms || 0
        maxResponseTime = Math.max(maxResponseTime, log.response_time_ms || 0)
      })

      // 计算平均响应时间
      Object.values(modelStats).forEach((stats: any) => {
        stats.avg_response_time_ms = stats.total_calls > 0
          ? Math.round(stats.total_response_time / stats.total_calls)
          : 0
        stats.success_rate = stats.total_calls > 0
          ? Math.round((stats.success_calls / stats.total_calls) * 100)
          : 0
        delete stats.total_response_time // 删除临时字段
      })

      return {
        date: today,
        date_only: today, // 为了兼容前端组件
        total_calls: totalCalls,
        success_calls: totalSuccessCalls,
        success_rate: totalCalls > 0 ? Math.round((totalSuccessCalls / totalCalls) * 100) : 0,
        total_cost_yuan: Number(totalCost.toFixed(4)),
        discount_cost_yuan: 0, // 暂时设为0，后续可以根据需要计算优惠
        total_tokens: totalTokens,
        total_input_tokens: totalInputTokens,
        total_output_tokens: totalOutputTokens,
        avg_response_time_ms: totalCalls > 0 ? Math.round(totalResponseTime / totalCalls) : 0,
        max_response_time_ms: maxResponseTime,
        model_breakdown: Object.values(modelStats),
        api_type_stats: apiTypeStats
      }
    } catch (error) {
      console.error('获取今日使用统计失败:', error)
      return null
    }
  }

  /**
   * 获取本月使用统计（按AI提供商分别统计）
   */
  static async getMonthlyUsage(): Promise<any> {
    try {
      const now = new Date()
      const yearMonth = now.toISOString().substring(0, 7) // YYYY-MM
      const monthStart = `${yearMonth}-01T00:00:00.000Z`
      const monthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999).toISOString()

      // 获取本月详细使用记录，按模型分组统计
      const { data: logs, error: logsError } = await supabase
        .from('ai_usage_logs')
        .select('*')
        .gte('created_at', monthStart)
        .lte('created_at', monthEnd)

      if (logsError) {
        console.error('获取本月使用日志失败:', logsError)
        return null
      }

      // 按模型分组统计
      const modelStats: { [key: string]: any } = {}
      let totalCalls = 0
      let totalCost = 0
      let totalTokens = 0
      let totalSuccessCalls = 0
      let totalResponseTime = 0

      logs?.forEach(log => {
        const model = log.model || 'unknown'
        if (!modelStats[model]) {
          modelStats[model] = {
            model,
            total_calls: 0,
            success_calls: 0,
            total_cost_yuan: 0,
            total_tokens: 0,
            input_tokens: 0,
            output_tokens: 0,
            avg_response_time_ms: 0,
            total_response_time: 0
          }
        }

        modelStats[model].total_calls++
        if (log.success) modelStats[model].success_calls++
        modelStats[model].total_cost_yuan += log.cost_yuan || 0
        modelStats[model].total_tokens += log.total_tokens || 0
        modelStats[model].input_tokens += log.input_tokens || 0
        modelStats[model].output_tokens += log.output_tokens || 0
        modelStats[model].total_response_time += log.response_time_ms || 0

        totalCalls++
        if (log.success) totalSuccessCalls++
        totalCost += log.cost_yuan || 0
        totalTokens += log.total_tokens || 0
        totalResponseTime += log.response_time_ms || 0
      })

      // 计算平均响应时间
      Object.values(modelStats).forEach((stats: any) => {
        stats.avg_response_time_ms = stats.total_calls > 0
          ? Math.round(stats.total_response_time / stats.total_calls)
          : 0
        stats.success_rate = stats.total_calls > 0
          ? Math.round((stats.success_calls / stats.total_calls) * 100)
          : 0
        delete stats.total_response_time // 删除临时字段
      })

      // 计算日均费用
      const currentDate = new Date()
      const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()
      const currentDay = currentDate.getDate()
      const avgDailyCost = totalCalls > 0 ? totalCost / currentDay : 0

      return {
        month: yearMonth,
        total_calls: totalCalls,
        success_calls: totalSuccessCalls,
        success_rate: totalCalls > 0 ? Math.round((totalSuccessCalls / totalCalls) * 100) : 0,
        total_cost_yuan: Number(totalCost.toFixed(4)),
        avg_daily_cost_yuan: Number(avgDailyCost.toFixed(4)),
        total_tokens: totalTokens,
        avg_response_time_ms: totalCalls > 0 ? Math.round(totalResponseTime / totalCalls) : 0,
        model_breakdown: Object.values(modelStats)
      }
    } catch (error) {
      console.error('获取本月使用统计失败:', error)
      return null
    }
  }

  /**
   * 获取最近几天的使用趋势
   */
  static async getRecentUsageTrend(days: number = 7): Promise<any[]> {
    try {
      const trendData = []
      const today = new Date()

      for (let i = 0; i < days; i++) {
        const date = new Date(today)
        date.setDate(date.getDate() - i)
        const dateStr = date.toISOString().split('T')[0]

        // 获取该日期的使用记录
        const { data: logs, error } = await supabase
          .from('ai_usage_logs')
          .select('*')
          .gte('created_at', `${dateStr}T00:00:00.000Z`)
          .lt('created_at', `${dateStr}T23:59:59.999Z`)

        if (error) {
          console.error(`获取${dateStr}使用记录失败:`, error)
          continue
        }

        // 计算该日统计
        let totalCalls = 0
        let totalCost = 0
        let totalTokens = 0
        let successCalls = 0

        logs?.forEach(log => {
          totalCalls++
          if (log.success) successCalls++
          totalCost += log.cost_yuan || 0
          totalTokens += log.total_tokens || 0
        })

        trendData.push({
          date_only: dateStr,
          total_calls: totalCalls,
          success_calls: successCalls,
          success_rate: totalCalls > 0 ? Math.round((successCalls / totalCalls) * 100) : 0,
          total_cost_yuan: Number(totalCost.toFixed(4)),
          total_tokens: totalTokens
        })
      }

      return trendData
    } catch (error) {
      console.error('获取使用趋势失败:', error)
      return []
    }
  }
}
