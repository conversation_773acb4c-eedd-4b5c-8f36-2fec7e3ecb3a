<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 分析质量测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .quality-score {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            margin-left: 10px;
        }
        .score-excellent {
            background: #28a745;
            color: white;
        }
        .score-good {
            background: #ffc107;
            color: black;
        }
        .score-poor {
            background: #dc3545;
            color: white;
        }
        .test-urls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .url-button {
            background: #6c757d;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .url-button:hover {
            background: #5a6268;
        }
        .analysis-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 10px;
        }
        .detail-item {
            margin-bottom: 10px;
        }
        .detail-label {
            font-weight: bold;
            color: #495057;
        }
        .detail-value {
            margin-left: 10px;
            color: #212529;
        }
        .tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 5px;
        }
        .tag {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 AI 分析质量测试</h1>
        <p>测试 GLM-4.5-air 的工具分析质量，确保返回详细的名称、描述、标签和分类</p>
        
        <div class="test-section">
            <h3>🎯 测试网站列表</h3>
            <p>点击下面的网站进行分析测试：</p>
            <div class="test-urls">
                <button class="url-button" onclick="testUrl('http://www.gebi1.com/')">戈壁东 - 在线计算器</button>
                <button class="url-button" onclick="testUrl('https://github.com')">GitHub - 代码托管</button>
                <button class="url-button" onclick="testUrl('https://www.figma.com')">Figma - 设计工具</button>
                <button class="url-button" onclick="testUrl('https://calculator.net')">Calculator.net - 计算器</button>
                <button class="url-button" onclick="testUrl('https://www.canva.com')">Canva - 设计平台</button>
                <button class="url-button" onclick="testUrl('https://notion.so')">Notion - 笔记工具</button>
                <button class="url-button" onclick="testUrl('https://www.zhihu.com')">知乎 - 问答社区</button>
                <button class="url-button" onclick="testUrl('https://www.bilibili.com')">哔哩哔哩 - 视频平台</button>
            </div>
            
            <div style="margin-top: 15px;">
                <input type="text" id="customUrl" placeholder="输入自定义URL进行测试" style="width: 300px; padding: 8px; border: 1px solid #ccc; border-radius: 4px;">
                <button class="test-button" onclick="testCustomUrl()">测试自定义URL</button>
            </div>
            
            <div id="analysisResults"></div>
        </div>

        <div class="test-section">
            <h3>📊 质量评估标准</h3>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 6px;">
                <h4>评分标准：</h4>
                <ul>
                    <li><strong>名称 (25分)</strong>: 准确反映网站身份，中文名称或官方名称</li>
                    <li><strong>描述 (35分)</strong>: 80-150字详细描述，包含主要功能、特色、适用场景</li>
                    <li><strong>标签 (25分)</strong>: 4-8个相关标签，包含功能、行业、技术标签</li>
                    <li><strong>分类 (15分)</strong>: 准确的三级分类，避免使用"其他"类别</li>
                </ul>
                <h4>质量等级：</h4>
                <ul>
                    <li><span class="quality-score score-excellent">优秀 (85-100分)</span>: 所有字段完整准确</li>
                    <li><span class="quality-score score-good">良好 (70-84分)</span>: 大部分字段准确，少量不足</li>
                    <li><span class="quality-score score-poor">较差 (0-69分)</span>: 多个字段不准确或缺失</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>🔄 批量测试</h3>
            <button class="test-button" onclick="runBatchTest()">运行批量测试</button>
            <button class="test-button" onclick="clearResults()">清空结果</button>
            <div id="batchResults"></div>
        </div>
    </div>

    <script>
        let testResults = [];

        // 测试单个URL
        async function testUrl(url) {
            const resultDiv = document.getElementById('analysisResults');
            resultDiv.innerHTML = `<div class="result info">正在分析 ${url}...</div>`;
            
            const startTime = Date.now();
            try {
                const response = await fetch('/api/analyze-url', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'test-key' // 需要实际的API密钥
                    },
                    body: JSON.stringify({ url })
                });

                const data = await response.json();
                const endTime = Date.now();
                const duration = endTime - startTime;

                if (response.ok) {
                    const quality = evaluateQuality(data);
                    displayAnalysisResult(url, data, quality, duration);
                    testResults.push({ url, data, quality, duration });
                } else {
                    throw new Error(data.error || '请求失败');
                }
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ 分析失败 (${duration}ms)
                        URL: ${url}
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 测试自定义URL
        function testCustomUrl() {
            const url = document.getElementById('customUrl').value.trim();
            if (!url) {
                alert('请输入URL');
                return;
            }
            testUrl(url);
        }

        // 评估分析质量
        function evaluateQuality(data) {
            let score = 0;
            let details = [];

            // 名称评估 (25分)
            if (data.name && data.name.length > 0) {
                if (data.name.length > 2 && !data.name.includes('未知') && !data.name.includes('网站')) {
                    score += 25;
                    details.push('✅ 名称: 准确 (+25分)');
                } else {
                    score += 10;
                    details.push('⚠️ 名称: 基本准确 (+10分)');
                }
            } else {
                details.push('❌ 名称: 缺失 (+0分)');
            }

            // 描述评估 (35分)
            if (data.description && data.description.length >= 80) {
                if (data.description.length >= 100 && 
                    data.description.includes('功能') && 
                    !data.description.includes('暂无') &&
                    !data.description.includes('未知')) {
                    score += 35;
                    details.push('✅ 描述: 详细准确 (+35分)');
                } else if (data.description.length >= 80) {
                    score += 25;
                    details.push('⚠️ 描述: 基本详细 (+25分)');
                } else {
                    score += 15;
                    details.push('⚠️ 描述: 较简单 (+15分)');
                }
            } else if (data.description && data.description.length > 0) {
                score += 10;
                details.push('❌ 描述: 过于简短 (+10分)');
            } else {
                details.push('❌ 描述: 缺失 (+0分)');
            }

            // 标签评估 (25分)
            if (data.tags && Array.isArray(data.tags) && data.tags.length >= 4) {
                if (data.tags.length >= 6 && data.tags.every(tag => tag && tag.length > 1)) {
                    score += 25;
                    details.push('✅ 标签: 丰富准确 (+25分)');
                } else if (data.tags.length >= 4) {
                    score += 20;
                    details.push('⚠️ 标签: 基本充足 (+20分)');
                } else {
                    score += 10;
                    details.push('⚠️ 标签: 数量不足 (+10分)');
                }
            } else if (data.tags && data.tags.length > 0) {
                score += 5;
                details.push('❌ 标签: 严重不足 (+5分)');
            } else {
                details.push('❌ 标签: 缺失 (+0分)');
            }

            // 分类评估 (15分)
            if (data.category && data.subcategory && data.subsubcategory) {
                if (data.category !== 'other' || data.subcategory !== 'utility') {
                    score += 15;
                    details.push('✅ 分类: 精确分类 (+15分)');
                } else {
                    score += 10;
                    details.push('⚠️ 分类: 使用通用分类 (+10分)');
                }
            } else {
                score += 5;
                details.push('❌ 分类: 不完整 (+5分)');
            }

            return { score, details };
        }

        // 显示分析结果
        function displayAnalysisResult(url, data, quality, duration) {
            const resultDiv = document.getElementById('analysisResults');
            
            let scoreClass = 'score-poor';
            let scoreText = '较差';
            if (quality.score >= 85) {
                scoreClass = 'score-excellent';
                scoreText = '优秀';
            } else if (quality.score >= 70) {
                scoreClass = 'score-good';
                scoreText = '良好';
            }

            resultDiv.innerHTML = `
                <div class="result ${quality.score >= 70 ? 'success' : quality.score >= 50 ? 'warning' : 'error'}">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <strong>分析结果: ${url}</strong>
                        <span class="quality-score ${scoreClass}">${scoreText} (${quality.score}/100)</span>
                    </div>
                    
                    <div class="analysis-details">
                        <div class="detail-item">
                            <span class="detail-label">名称:</span>
                            <span class="detail-value">${data.name || '未获取'}</span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">描述:</span>
                            <span class="detail-value">${data.description || '未获取'}</span>
                            <small style="color: #6c757d; margin-left: 10px;">(${(data.description || '').length} 字)</small>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">标签:</span>
                            <div class="tags">
                                ${(data.tags || []).map(tag => `<span class="tag">${tag}</span>`).join('')}
                            </div>
                            <small style="color: #6c757d;">(${(data.tags || []).length} 个标签)</small>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">分类:</span>
                            <span class="detail-value">${data.category || '未知'} > ${data.subcategory || '未知'} > ${data.subsubcategory || '未知'}</span>
                        </div>
                        
                        <div class="detail-item">
                            <span class="detail-label">响应时间:</span>
                            <span class="detail-value">${duration}ms</span>
                        </div>
                    </div>
                    
                    <div style="margin-top: 15px;">
                        <strong>质量评估详情:</strong>
                        <ul style="margin: 10px 0; padding-left: 20px;">
                            ${quality.details.map(detail => `<li>${detail}</li>`).join('')}
                        </ul>
                    </div>
                </div>
            `;
        }

        // 运行批量测试
        async function runBatchTest() {
            const testUrls = [
                'http://www.gebi1.com/',
                'https://github.com',
                'https://www.figma.com',
                'https://calculator.net',
                'https://www.canva.com'
            ];

            const batchDiv = document.getElementById('batchResults');
            batchDiv.innerHTML = '<div class="result info">正在运行批量测试...</div>';

            testResults = [];
            
            for (let i = 0; i < testUrls.length; i++) {
                const url = testUrls[i];
                batchDiv.innerHTML += `<div class="result info">测试 ${i + 1}/${testUrls.length}: ${url}</div>`;
                
                try {
                    await testUrl(url);
                    await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒避免频率限制
                } catch (error) {
                    console.error('批量测试错误:', error);
                }
            }

            // 显示批量测试总结
            displayBatchSummary();
        }

        // 显示批量测试总结
        function displayBatchSummary() {
            const batchDiv = document.getElementById('batchResults');
            
            if (testResults.length === 0) {
                batchDiv.innerHTML = '<div class="result warning">没有测试结果</div>';
                return;
            }

            const avgScore = testResults.reduce((sum, result) => sum + result.quality.score, 0) / testResults.length;
            const avgDuration = testResults.reduce((sum, result) => sum + result.duration, 0) / testResults.length;
            
            const excellentCount = testResults.filter(r => r.quality.score >= 85).length;
            const goodCount = testResults.filter(r => r.quality.score >= 70 && r.quality.score < 85).length;
            const poorCount = testResults.filter(r => r.quality.score < 70).length;

            batchDiv.innerHTML = `
                <div class="result success">
                    <h4>📊 批量测试总结</h4>
                    
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #007bff;">${testResults.length}</div>
                            <div style="font-size: 12px; color: #666;">测试总数</div>
                        </div>
                        
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #28a745;">${avgScore.toFixed(1)}</div>
                            <div style="font-size: 12px; color: #666;">平均分数</div>
                        </div>
                        
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center;">
                            <div style="font-size: 24px; font-weight: bold; color: #ffc107;">${avgDuration.toFixed(0)}ms</div>
                            <div style="font-size: 12px; color: #666;">平均响应时间</div>
                        </div>
                    </div>
                    
                    <div style="margin: 15px 0;">
                        <h5>质量分布:</h5>
                        <ul>
                            <li>优秀 (85-100分): ${excellentCount} 个</li>
                            <li>良好 (70-84分): ${goodCount} 个</li>
                            <li>较差 (0-69分): ${poorCount} 个</li>
                        </ul>
                    </div>
                    
                    <div style="margin: 15px 0;">
                        <h5>详细结果:</h5>
                        ${testResults.map(result => `
                            <div style="margin: 5px 0; padding: 8px; background: #e9ecef; border-radius: 4px;">
                                <strong>${result.url}</strong> - 
                                <span class="quality-score ${result.quality.score >= 85 ? 'score-excellent' : result.quality.score >= 70 ? 'score-good' : 'score-poor'}">
                                    ${result.quality.score}分
                                </span>
                                (${result.duration}ms)
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        // 清空结果
        function clearResults() {
            document.getElementById('analysisResults').innerHTML = '';
            document.getElementById('batchResults').innerHTML = '';
            testResults = [];
        }
    </script>
</body>
</html>
