<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI使用监控测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 AI使用监控测试</h1>
        <p>测试所有AI功能的使用监控是否正常工作</p>
        
        <div class="test-section">
            <h3>📊 当前AI使用统计</h3>
            <button class="test-button" onclick="getCurrentStats()">🔄 获取当前统计</button>
            <div id="currentStats"></div>
        </div>

        <div class="test-section">
            <h3>🧪 AI功能测试</h3>
            <p>测试各个AI功能，验证使用监控是否正常记录</p>
            
            <div style="margin-bottom: 15px;">
                <h4>URL分析测试</h4>
                <button class="test-button" onclick="testAnalyzeUrl()">测试URL分析</button>
                <div id="analyzeUrlResult"></div>
            </div>

            <div style="margin-bottom: 15px;">
                <h4>添加工具测试</h4>
                <button class="test-button" onclick="testAddTool()">测试添加工具</button>
                <div id="addToolResult"></div>
            </div>

            <div style="margin-bottom: 15px;">
                <h4>深度搜索测试</h4>
                <button class="test-button" onclick="testDeepSearch()">测试深度搜索</button>
                <div id="deepSearchResult"></div>
            </div>

            <div style="margin-bottom: 15px;">
                <h4>全网搜索测试</h4>
                <button class="test-button" onclick="testGlobalSearch()">测试全网搜索</button>
                <div id="globalSearchResult"></div>
            </div>

            <div style="margin-bottom: 15px;">
                <h4>文本导入测试</h4>
                <button class="test-button" onclick="testTextImport()">测试文本导入</button>
                <div id="textImportResult"></div>
            </div>
        </div>

        <div class="test-section">
            <h3>📈 监控验证</h3>
            <p>执行AI功能后，验证统计数据是否实时更新</p>
            <button class="test-button" onclick="runFullTest()">🚀 运行完整测试</button>
            <div id="fullTestResult"></div>
        </div>
    </div>

    <script>
        let initialStats = null;

        // 获取当前AI使用统计
        async function getCurrentStats() {
            const resultDiv = document.getElementById('currentStats');
            resultDiv.innerHTML = '<div class="result info">正在获取统计数据...</div>';
            
            try {
                const response = await fetch('/api/ai-usage-stats?type=today');
                const data = await response.json();

                if (response.ok && data.success) {
                    const stats = data.data;
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ 今日AI使用统计:
                            
                            - 总调用次数: ${stats.total_calls}
                            - 成功率: ${stats.success_rate}%
                            - 总费用: ¥${stats.total_cost_yuan}
                            - 平均响应时间: ${stats.avg_response_time_ms}ms
                            - 总tokens: ${stats.total_tokens}
                            
                            模型分布:
                            ${stats.model_breakdown?.map(model => 
                                `- ${model.model}: ${model.total_calls}次调用, ¥${model.total_cost_yuan}`
                            ).join('\n') || '暂无数据'}
                        </div>
                    `;
                    return stats;
                } else {
                    throw new Error(data.error || '获取统计失败');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ 获取统计失败
                        
                        错误: ${error.message}
                    </div>
                `;
                return null;
            }
        }

        // 测试URL分析
        async function testAnalyzeUrl() {
            const resultDiv = document.getElementById('analyzeUrlResult');
            resultDiv.innerHTML = '<div class="result info">正在测试URL分析...</div>';
            
            const startTime = Date.now();
            try {
                const response = await fetch('/api/analyze-url', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'test-key' // 需要实际的API密钥
                    },
                    body: JSON.stringify({
                        url: 'https://github.com'
                    })
                });

                const data = await response.json();
                const endTime = Date.now();
                const duration = endTime - startTime;

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ URL分析测试成功 (${duration}ms)
                            
                            结果:
                            - 名称: ${data.name}
                            - 描述: ${data.description?.substring(0, 100)}...
                            - 标签: ${data.tags?.join(', ')}
                        </div>
                    `;
                } else {
                    throw new Error(data.error || '请求失败');
                }
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ URL分析测试失败 (${duration}ms)
                        
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 测试添加工具
        async function testAddTool() {
            const resultDiv = document.getElementById('addToolResult');
            resultDiv.innerHTML = '<div class="result info">正在测试添加工具...</div>';
            
            const startTime = Date.now();
            try {
                const response = await fetch('/api/add-tool', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'test-key'
                    },
                    body: JSON.stringify({
                        url: 'https://www.figma.com'
                    })
                });

                const data = await response.json();
                const endTime = Date.now();
                const duration = endTime - startTime;

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ 添加工具测试成功 (${duration}ms)
                            
                            结果:
                            - 工具名称: ${data.tool?.name}
                            - 分析状态: ${data.analysis ? '成功' : '失败'}
                        </div>
                    `;
                } else {
                    throw new Error(data.error || '请求失败');
                }
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ 添加工具测试失败 (${duration}ms)
                        
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 测试深度搜索
        async function testDeepSearch() {
            const resultDiv = document.getElementById('deepSearchResult');
            resultDiv.innerHTML = '<div class="result info">正在测试深度搜索...</div>';
            
            const startTime = Date.now();
            try {
                const response = await fetch('/api/deep-search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'test-key'
                    },
                    body: JSON.stringify({
                        query: '代码编辑器',
                        tools: [],
                        useOptimized: true
                    })
                });

                const data = await response.json();
                const endTime = Date.now();
                const duration = endTime - startTime;

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ 深度搜索测试成功 (${duration}ms)
                            
                            结果:
                            - 搜索总结: ${data.data?.searchSummary}
                            - 推荐工具数: ${data.data?.recommendedTools?.length || 0}
                        </div>
                    `;
                } else {
                    throw new Error(data.error || '请求失败');
                }
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ 深度搜索测试失败 (${duration}ms)
                        
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 测试全网搜索
        async function testGlobalSearch() {
            const resultDiv = document.getElementById('globalSearchResult');
            resultDiv.innerHTML = '<div class="result info">正在测试全网搜索...</div>';
            
            const startTime = Date.now();
            try {
                const response = await fetch('/api/global-search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'test-key'
                    },
                    body: JSON.stringify({
                        query: '在线设计工具',
                        useOptimized: true
                    })
                });

                const data = await response.json();
                const endTime = Date.now();
                const duration = endTime - startTime;

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ 全网搜索测试成功 (${duration}ms)
                            
                            结果:
                            - 搜索总结: ${data.data?.searchSummary}
                            - 推荐工具数: ${data.data?.recommendedTools?.length || 0}
                        </div>
                    `;
                } else {
                    throw new Error(data.error || '请求失败');
                }
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ 全网搜索测试失败 (${duration}ms)
                        
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 测试文本导入（模拟）
        async function testTextImport() {
            const resultDiv = document.getElementById('textImportResult');
            resultDiv.innerHTML = `
                <div class="result info">
                    📝 文本导入测试说明:
                    
                    文本导入功能使用 BatchImportService，
                    该服务调用 AIService.analyzeBatchContent 方法，
                    该方法已包含AI使用监控。
                    
                    要测试文本导入，请在主界面的数据管理中心使用文本导入功能。
                </div>
            `;
        }

        // 运行完整测试
        async function runFullTest() {
            const resultDiv = document.getElementById('fullTestResult');
            resultDiv.innerHTML = '<div class="result info">正在运行完整测试...</div>';
            
            try {
                // 1. 获取初始统计
                resultDiv.innerHTML += '<div class="result info">步骤1: 获取初始统计...</div>';
                initialStats = await getCurrentStats();
                
                if (!initialStats) {
                    throw new Error('无法获取初始统计数据');
                }

                // 2. 执行一个AI功能测试
                resultDiv.innerHTML += '<div class="result info">步骤2: 执行URL分析测试...</div>';
                await testAnalyzeUrl();
                
                // 3. 等待一下让监控记录完成
                resultDiv.innerHTML += '<div class="result info">步骤3: 等待监控记录完成...</div>';
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 4. 获取更新后的统计
                resultDiv.innerHTML += '<div class="result info">步骤4: 获取更新后的统计...</div>';
                const updatedStats = await getCurrentStats();
                
                if (!updatedStats) {
                    throw new Error('无法获取更新后的统计数据');
                }

                // 5. 比较统计数据
                const callsIncreased = updatedStats.total_calls > initialStats.total_calls;
                const costIncreased = updatedStats.total_cost_yuan > initialStats.total_cost_yuan;
                
                if (callsIncreased || costIncreased) {
                    resultDiv.innerHTML += `
                        <div class="result success">
                            ✅ AI使用监控测试成功！
                            
                            统计对比:
                            - 调用次数: ${initialStats.total_calls} → ${updatedStats.total_calls} ${callsIncreased ? '✅' : '❌'}
                            - 总费用: ¥${initialStats.total_cost_yuan} → ¥${updatedStats.total_cost_yuan} ${costIncreased ? '✅' : '❌'}
                            
                            结论: AI使用监控正常工作，统计数据实时更新！
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML += `
                        <div class="result error">
                            ❌ AI使用监控可能有问题
                            
                            统计对比:
                            - 调用次数: ${initialStats.total_calls} → ${updatedStats.total_calls}
                            - 总费用: ¥${initialStats.total_cost_yuan} → ¥${updatedStats.total_cost_yuan}
                            
                            统计数据没有变化，请检查监控逻辑。
                        </div>
                    `;
                }
                
            } catch (error) {
                resultDiv.innerHTML += `
                    <div class="result error">
                        ❌ 完整测试失败
                        
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 页面加载时获取初始统计
        window.onload = function() {
            getCurrentStats();
        };
    </script>
</body>
</html>
