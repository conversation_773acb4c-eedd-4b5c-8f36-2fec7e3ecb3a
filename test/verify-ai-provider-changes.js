#!/usr/bin/env node

/**
 * AI 提供商切换验证脚本
 * 验证所有相关文件是否正确更新
 */

const fs = require('fs');
const path = require('path');

// 需要检查的文件和预期内容
const checks = [
  {
    file: '.env.example',
    checks: [
      { pattern: /AI_PROVIDER=glm/, description: '环境变量 AI_PROVIDER 设置为 glm' },
      { pattern: /GLM_API_KEY=/, description: 'GLM API 密钥配置' },
      { pattern: /GLM_API_URL=/, description: 'GLM API URL 配置' },
      { pattern: /DEEPSEEK_API_KEY=/, description: 'DeepSeek API 密钥配置（保留）' }
    ]
  },
  {
    file: 'lib/ai-providers.ts',
    checks: [
      { pattern: /class GLMProvider/, description: 'GLM 提供商类' },
      { pattern: /class DeepSeekProvider/, description: 'DeepSeek 提供商类' },
      { pattern: /class AIServiceFactory/, description: 'AI 服务工厂类' },
      { pattern: /glm-4\.5-air/, description: 'GLM 模型名称' }
    ]
  },
  {
    file: 'lib/ai-service.ts',
    checks: [
      { pattern: /callAIAPI/, description: '使用新的 AI API 调用方法' },
      { pattern: /AIServiceFactory\.getProvider/, description: '使用 AI 服务工厂' },
      { pattern: /import.*ai-providers/, description: '导入 AI 提供商模块' }
    ]
  },
  {
    file: 'lib/ai-service-optimized.ts',
    checks: [
      { pattern: /callAIAPIWithRetry/, description: '优化版使用新的 AI API 调用' },
      { pattern: /import.*ai-providers/, description: '导入 AI 提供商模块' }
    ]
  },
  {
    file: 'app/api/analyze-url/route.ts',
    checks: [
      { pattern: /AIServiceFactory\.getProvider/, description: 'API 路由使用新的提供商' },
      { pattern: /import.*ai-providers/, description: '导入 AI 提供商模块' }
    ]
  },
  {
    file: 'app/api/add-tool/route.ts',
    checks: [
      { pattern: /AIServiceFactory\.getProvider/, description: 'API 路由使用新的提供商' },
      { pattern: /import.*ai-providers/, description: '导入 AI 提供商模块' }
    ]
  },
  {
    file: 'lib/ai-usage-monitor.ts',
    checks: [
      { pattern: /AI_PRICING/, description: '更新为支持多提供商的价格配置' },
      { pattern: /glm-4\.5-air/, description: '默认模型更新为 GLM' },
      { pattern: /model\.includes\('glm'\)/, description: 'GLM 模型费用计算逻辑' }
    ]
  }
];

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFile(filePath, fileChecks) {
  const fullPath = path.join(process.cwd(), filePath);
  
  if (!fs.existsSync(fullPath)) {
    log(`❌ 文件不存在: ${filePath}`, 'red');
    return false;
  }

  const content = fs.readFileSync(fullPath, 'utf8');
  let allPassed = true;

  log(`\n📁 检查文件: ${filePath}`, 'blue');

  fileChecks.forEach(check => {
    const pattern = check.pattern;
    const description = check.description;

    if (pattern.test(content)) {
      log(`  ✅ ${description}`, 'green');
    } else {
      log(`  ❌ ${description}`, 'red');
      allPassed = false;
    }
  });

  return allPassed;
}

function main() {
  log('🚀 开始验证 AI 提供商切换修改...', 'bold');
  
  let totalChecks = 0;
  let passedChecks = 0;
  let failedFiles = [];

  checks.forEach(({ file, checks: fileChecks }) => {
    const passed = checkFile(file, fileChecks);
    totalChecks++;
    
    if (passed) {
      passedChecks++;
    } else {
      failedFiles.push(file);
    }
  });

  // 输出总结
  log('\n📊 验证结果总结:', 'bold');
  log(`总文件数: ${totalChecks}`);
  log(`通过验证: ${passedChecks}`, passedChecks === totalChecks ? 'green' : 'yellow');
  log(`验证失败: ${totalChecks - passedChecks}`, totalChecks - passedChecks === 0 ? 'green' : 'red');

  if (failedFiles.length > 0) {
    log('\n❌ 以下文件验证失败:', 'red');
    failedFiles.forEach(file => {
      log(`  - ${file}`, 'red');
    });
  }

  // 额外检查
  log('\n🔍 额外检查:', 'bold');
  
  // 检查是否有遗留的 DeepSeek 直接调用
  const filesToScan = [
    'lib/ai-service.ts',
    'lib/ai-service-optimized.ts',
    'app/api/analyze-url/route.ts',
    'app/api/add-tool/route.ts'
  ];

  let hasLegacyCalls = false;
  filesToScan.forEach(file => {
    const fullPath = path.join(process.cwd(), file);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // 检查是否还有直接的 DeepSeek API 调用
      if (content.includes('callDeepSeekAPI') && !content.includes('// 已废弃')) {
        log(`  ⚠️  ${file} 中可能还有遗留的 DeepSeek API 调用`, 'yellow');
        hasLegacyCalls = true;
      }
      
      // 检查是否还有硬编码的 DeepSeek URL
      if (content.includes('api.deepseek.com') && !content.includes('// 备用')) {
        log(`  ⚠️  ${file} 中可能还有硬编码的 DeepSeek URL`, 'yellow');
        hasLegacyCalls = true;
      }
    }
  });

  if (!hasLegacyCalls) {
    log('  ✅ 没有发现遗留的 DeepSeek 直接调用', 'green');
  }

  // 最终结果
  if (passedChecks === totalChecks && !hasLegacyCalls) {
    log('\n🎉 所有验证通过！AI 提供商切换修改完成。', 'green');
    log('\n📝 下一步操作:', 'bold');
    log('1. 设置环境变量 GLM_API_KEY');
    log('2. 设置环境变量 AI_PROVIDER=glm');
    log('3. 重启应用服务器');
    log('4. 运行测试页面: /test/test-ai-provider-switch.html');
    return 0;
  } else {
    log('\n❌ 验证未完全通过，请检查上述问题。', 'red');
    return 1;
  }
}

// 运行验证
if (require.main === module) {
  process.exit(main());
}

module.exports = { main, checkFile };
