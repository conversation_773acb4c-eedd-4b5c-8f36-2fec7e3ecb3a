<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 服务提供商切换测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .provider-status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .provider-glm {
            background: #28a745;
            color: white;
        }
        .provider-deepseek {
            background: #6f42c1;
            color: white;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 AI 服务提供商切换测试</h1>
        <p>测试 GLM-4.5-air 和 DeepSeek 之间的切换功能</p>
        
        <div class="test-section">
            <h3>📊 当前状态</h3>
            <p>当前AI提供商: <span id="currentProvider">检测中...</span></p>
            <p>环境变量状态: <span id="envStatus">检查中...</span></p>
            <button class="test-button" onclick="checkCurrentStatus()">🔄 刷新状态</button>
        </div>

        <div class="test-section">
            <h3>🧪 API 功能测试</h3>
            <div>
                <button class="test-button" onclick="testAnalyzeUrl()">测试 URL 分析</button>
                <button class="test-button" onclick="testDeepSearch()">测试深度搜索</button>
                <button class="test-button" onclick="testGlobalSearch()">测试全网搜索</button>
                <button class="test-button" onclick="testAddTool()">测试添加工具</button>
            </div>
            <div id="apiTestResults"></div>
        </div>

        <div class="test-section">
            <h3>💰 费用计算测试</h3>
            <div>
                <button class="test-button" onclick="testCostCalculation()">测试费用计算</button>
                <button class="test-button" onclick="getAIUsageStats()">获取使用统计</button>
            </div>
            <div id="costTestResults"></div>
        </div>

        <div class="test-section">
            <h3>🔄 提供商切换测试</h3>
            <p><strong>注意:</strong> 这些测试需要在服务器端修改环境变量</p>
            <div>
                <button class="test-button" onclick="simulateProviderSwitch('glm')">模拟切换到 GLM</button>
                <button class="test-button" onclick="simulateProviderSwitch('deepseek')">模拟切换到 DeepSeek</button>
            </div>
            <div id="switchTestResults"></div>
        </div>

        <div class="test-section">
            <h3>📈 性能对比</h3>
            <div>
                <button class="test-button" onclick="performanceTest()">运行性能测试</button>
            </div>
            <div id="performanceResults"></div>
        </div>
    </div>

    <script>
        let testResults = {
            glm: { success: 0, failed: 0, totalTime: 0 },
            deepseek: { success: 0, failed: 0, totalTime: 0 }
        };

        // 检查当前状态
        async function checkCurrentStatus() {
            try {
                const response = await fetch('/api/search-config');
                const data = await response.json();
                
                if (data.success) {
                    const provider = data.data.currentConfig?.useOptimizedSearch ? 'GLM (优化版)' : 'GLM (标准版)';
                    document.getElementById('currentProvider').innerHTML = 
                        `<span class="provider-status provider-glm">${provider}</span>`;
                    document.getElementById('envStatus').innerHTML = 
                        '<span style="color: green;">✅ 配置正常</span>';
                } else {
                    throw new Error('配置获取失败');
                }
            } catch (error) {
                document.getElementById('currentProvider').innerHTML = 
                    '<span style="color: red;">❌ 检测失败</span>';
                document.getElementById('envStatus').innerHTML = 
                    '<span style="color: red;">❌ 配置异常</span>';
                console.error('状态检查失败:', error);
            }
        }

        // 测试 URL 分析
        async function testAnalyzeUrl() {
            const resultDiv = document.getElementById('apiTestResults');
            resultDiv.innerHTML = '<div class="result info">正在测试 URL 分析...</div>';
            
            const startTime = Date.now();
            try {
                const response = await fetch('/api/analyze-url', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'test-key' // 需要实际的API密钥
                    },
                    body: JSON.stringify({
                        url: 'https://github.com'
                    })
                });

                const data = await response.json();
                const endTime = Date.now();
                const duration = endTime - startTime;

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ URL 分析测试成功 (${duration}ms)
                            
                            结果:
                            - 名称: ${data.name}
                            - 描述: ${data.description}
                            - 标签: ${data.tags?.join(', ')}
                            - 分类: ${data.category}/${data.subcategory}/${data.subsubcategory}
                        </div>
                    `;
                } else {
                    throw new Error(data.error || '请求失败');
                }
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ URL 分析测试失败 (${duration}ms)
                        
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 测试深度搜索
        async function testDeepSearch() {
            const resultDiv = document.getElementById('apiTestResults');
            resultDiv.innerHTML = '<div class="result info">正在测试深度搜索...</div>';
            
            const startTime = Date.now();
            try {
                const response = await fetch('/api/deep-search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'test-key'
                    },
                    body: JSON.stringify({
                        query: '代码编辑器',
                        tools: [], // 简化测试
                        useOptimized: true
                    })
                });

                const data = await response.json();
                const endTime = Date.now();
                const duration = endTime - startTime;

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ 深度搜索测试成功 (${duration}ms)
                            
                            结果:
                            - 搜索总结: ${data.data?.searchSummary}
                            - 推荐工具数: ${data.data?.recommendedTools?.length || 0}
                            - 搜索洞察: ${data.data?.searchInsights}
                        </div>
                    `;
                } else {
                    throw new Error(data.error || '请求失败');
                }
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ 深度搜索测试失败 (${duration}ms)
                        
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 测试全网搜索
        async function testGlobalSearch() {
            const resultDiv = document.getElementById('apiTestResults');
            resultDiv.innerHTML = '<div class="result info">正在测试全网搜索...</div>';
            
            const startTime = Date.now();
            try {
                const response = await fetch('/api/global-search', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'test-key'
                    },
                    body: JSON.stringify({
                        query: '在线设计工具',
                        useOptimized: true
                    })
                });

                const data = await response.json();
                const endTime = Date.now();
                const duration = endTime - startTime;

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ 全网搜索测试成功 (${duration}ms)
                            
                            结果:
                            - 搜索总结: ${data.data?.searchSummary}
                            - 推荐工具数: ${data.data?.recommendedTools?.length || 0}
                            - 搜索洞察: ${data.data?.searchInsights}
                        </div>
                    `;
                } else {
                    throw new Error(data.error || '请求失败');
                }
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ 全网搜索测试失败 (${duration}ms)
                        
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 测试添加工具
        async function testAddTool() {
            const resultDiv = document.getElementById('apiTestResults');
            resultDiv.innerHTML = '<div class="result info">正在测试添加工具...</div>';
            
            const startTime = Date.now();
            try {
                const response = await fetch('/api/add-tool', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-API-Key': 'test-key'
                    },
                    body: JSON.stringify({
                        url: 'https://www.figma.com'
                    })
                });

                const data = await response.json();
                const endTime = Date.now();
                const duration = endTime - startTime;

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ 添加工具测试成功 (${duration}ms)
                            
                            结果:
                            - 工具名称: ${data.tool?.name}
                            - 分析结果: ${JSON.stringify(data.analysis, null, 2)}
                        </div>
                    `;
                } else {
                    throw new Error(data.error || '请求失败');
                }
            } catch (error) {
                const endTime = Date.now();
                const duration = endTime - startTime;
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ 添加工具测试失败 (${duration}ms)
                        
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 测试费用计算
        async function testCostCalculation() {
            const resultDiv = document.getElementById('costTestResults');
            resultDiv.innerHTML = '<div class="result info">正在测试费用计算...</div>';
            
            // 模拟不同的token使用情况
            const testCases = [
                { input: 1000, output: 500, model: 'glm-4.5-air' },
                { input: 1000, output: 500, model: 'deepseek-chat' },
                { input: 10000, output: 5000, model: 'glm-4.5-air' },
                { input: 10000, output: 5000, model: 'deepseek-chat' }
            ];

            let results = '费用计算测试结果:\n\n';
            
            testCases.forEach((testCase, index) => {
                // 这里应该调用实际的费用计算函数
                // 由于是前端测试，我们模拟计算结果
                let cost = 0;
                if (testCase.model.includes('glm')) {
                    cost = (testCase.input / 1000000) * 0.5 + (testCase.output / 1000000) * 1.5;
                } else {
                    cost = (testCase.input / 1000000) * 2.0 + (testCase.output / 1000000) * 8.0;
                }
                
                results += `测试 ${index + 1}: ${testCase.model}\n`;
                results += `  输入: ${testCase.input} tokens\n`;
                results += `  输出: ${testCase.output} tokens\n`;
                results += `  费用: ¥${cost.toFixed(6)}\n\n`;
            });

            resultDiv.innerHTML = `<div class="result success">✅ ${results}</div>`;
        }

        // 获取AI使用统计
        async function getAIUsageStats() {
            const resultDiv = document.getElementById('costTestResults');
            resultDiv.innerHTML = '<div class="result info">正在获取使用统计...</div>';
            
            try {
                const response = await fetch('/api/ai-usage-stats?type=today');
                const data = await response.json();

                if (response.ok && data.success) {
                    const stats = data.data;
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ 今日AI使用统计:
                            
                            - 总调用次数: ${stats.total_calls}
                            - 成功率: ${stats.success_rate}%
                            - 总费用: ¥${stats.total_cost_yuan}
                            - 平均响应时间: ${stats.avg_response_time_ms}ms
                            - 总tokens: ${stats.total_tokens}
                        </div>
                    `;
                } else {
                    throw new Error(data.error || '获取统计失败');
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ 获取使用统计失败
                        
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 模拟提供商切换
        function simulateProviderSwitch(provider) {
            const resultDiv = document.getElementById('switchTestResults');
            resultDiv.innerHTML = `
                <div class="result info">
                    📝 模拟切换到 ${provider.toUpperCase()}
                    
                    要实际切换提供商，请修改环境变量:
                    AI_PROVIDER=${provider}
                    
                    然后重启应用服务器。
                </div>
            `;
        }

        // 性能测试
        async function performanceTest() {
            const resultDiv = document.getElementById('performanceResults');
            resultDiv.innerHTML = '<div class="result info">正在运行性能测试...</div>';
            
            // 这里可以添加实际的性能测试逻辑
            // 比如连续调用API并测量响应时间
            
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <div class="result success">
                        ✅ 性能测试完成
                        
                        注意: 实际性能测试需要在真实环境中进行，
                        包括网络延迟、API限流等因素的考虑。
                        
                        建议的测试指标:
                        - 平均响应时间
                        - 成功率
                        - 并发处理能力
                        - 错误恢复能力
                    </div>
                `;
            }, 2000);
        }

        // 页面加载时检查状态
        window.onload = function() {
            checkCurrentStatus();
        };
    </script>
</body>
</html>
