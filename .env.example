# ToolMaster 环境变量配置模板
# 复制此文件为 .env.local 并填入实际值

# ===========================================
# 页面访问密码配置
# ===========================================
# 多个密码用逗号分隔，用户输入其中任意一个即可访问
ACCESS_PASSWORDS=toolmaster2024,admin123456,secure2024

# ===========================================
# AI 服务配置
# ===========================================
# AI 服务提供商选择: 'glm' | 'deepseek' (默认: glm)
AI_PROVIDER=glm

# GLM-4.5-air 配置 (主要AI服务)
GLM_API_KEY=your-glm-api-key-here
GLM_API_URL=https://open.bigmodel.cn/api/paas/v4/chat/completions
GLM_MODEL=glm-4.5-air

# DeepSeek AI 配置 (备用AI服务)
DEEPSEEK_API_KEY=your-deepseek-api-key-here
DEEPSEEK_API_URL=https://api.deepseek.com/chat/completions
DEEPSEEK_MODEL=deepseek-chat

# ===========================================
# Supabase 数据库配置
# ===========================================
# Supabase 项目 URL (必填)
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url

# Supabase 匿名密钥 (必填)
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# ===========================================
# API 安全配置
# ===========================================
# API 访问密钥 (必填) - 用于保护API路由
API_ACCESS_KEY=your-random-api-access-key-here

# ===========================================
# 配置说明
# ===========================================
# 1. ACCESS_PASSWORDS: 设置页面访问密码，支持多个密码
# 2. AI_PROVIDER: 选择AI服务提供商 (glm/deepseek)
# 3. GLM_API_KEY: 从 https://open.bigmodel.cn/ 获取
# 4. DEEPSEEK_API_KEY: 从 https://platform.deepseek.com/ 获取
# 5. NEXT_PUBLIC_SUPABASE_URL 和 NEXT_PUBLIC_SUPABASE_ANON_KEY: 从 https://supabase.com/ 项目设置中获取
# 6. API_ACCESS_KEY: 设置API访问密钥，用于保护API路由安全

---
# 启用优化版搜索（false-原版,true-优化版）
USE_OPTIMIZED_SEARCH=false
# 设置超时时间
DEEP_SEARCH_TIMEOUT=30000
GLOBAL_SEARCH_TIMEOUT=45000

