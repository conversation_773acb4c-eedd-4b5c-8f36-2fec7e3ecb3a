#!/usr/bin/env node

/**
 * ToolMaster 部署前检查脚本
 * 验证项目是否准备好部署到 Vercel
 */

const fs = require('fs');
const path = require('path');

// 颜色输出
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 检查项目
const checks = [
  {
    name: '检查 package.json',
    check: () => {
      const packagePath = path.join(process.cwd(), 'package.json');
      if (!fs.existsSync(packagePath)) {
        return { success: false, message: 'package.json 不存在' };
      }

      const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

      if (!pkg.scripts || !pkg.scripts.build) {
        return { success: false, message: 'package.json 中缺少 build 脚本' };
      }

      if (!pkg.dependencies || !pkg.dependencies.next) {
        return { success: false, message: 'package.json 中缺少 Next.js 依赖' };
      }

      return { success: true, message: 'package.json 配置正确' };
    }
  },
  {
    name: '检查 Next.js 配置',
    check: () => {
      const nextConfigPath = path.join(process.cwd(), 'next.config.mjs');
      if (!fs.existsSync(nextConfigPath)) {
        return { success: false, message: 'next.config.mjs 不存在' };
      }
      return { success: true, message: 'Next.js 配置文件存在' };
    }
  },
  {
    name: '检查 Vercel 配置',
    check: () => {
      const vercelConfigPath = path.join(process.cwd(), 'vercel.json');
      if (!fs.existsSync(vercelConfigPath)) {
        return { success: false, message: 'vercel.json 不存在' };
      }

      try {
        const config = JSON.parse(fs.readFileSync(vercelConfigPath, 'utf8'));
        if (!config.functions) {
          return { success: false, message: 'vercel.json 中缺少 functions 配置' };
        }
        return { success: true, message: 'Vercel 配置正确' };
      } catch (error) {
        return { success: false, message: 'vercel.json 格式错误' };
      }
    }
  },
  {
    name: '检查环境变量模板',
    check: () => {
      const envExamplePath = path.join(process.cwd(), '.env.example');
      if (!fs.existsSync(envExamplePath)) {
        return { success: false, message: '.env.example 不存在' };
      }

      const envContent = fs.readFileSync(envExamplePath, 'utf8');
      const requiredVars = [
        'AI_PROVIDER',
        'GLM_API_KEY',
        'DEEPSEEK_API_KEY',
        'NEXT_PUBLIC_SUPABASE_URL',
        'NEXT_PUBLIC_SUPABASE_ANON_KEY',
        'ACCESS_PASSWORDS',
        'API_ACCESS_KEY'
      ];

      const missingVars = requiredVars.filter(varName => !envContent.includes(varName));

      if (missingVars.length > 0) {
        return {
          success: false,
          message: `缺少环境变量: ${missingVars.join(', ')}`
        };
      }

      return { success: true, message: '环境变量模板完整' };
    }
  },
  {
    name: '检查 API 路由',
    check: () => {
      const apiDir = path.join(process.cwd(), 'app', 'api');
      if (!fs.existsSync(apiDir)) {
        return { success: false, message: 'app/api 目录不存在' };
      }

      const requiredApis = [
        'analyze-url',
        'add-tool',
        'deep-search',
        'global-search',
        'ai-usage-stats'
      ];

      const missingApis = requiredApis.filter(api => {
        const apiPath = path.join(apiDir, api, 'route.ts');
        return !fs.existsSync(apiPath);
      });

      if (missingApis.length > 0) {
        return {
          success: false,
          message: `缺少 API 路由: ${missingApis.join(', ')}`
        };
      }

      return { success: true, message: '所有必需的 API 路由存在' };
    }
  },
  {
    name: '检查核心库文件',
    check: () => {
      const requiredLibs = [
        'lib/ai-providers.ts',
        'lib/ai-service.ts',
        'lib/ai-usage-monitor.ts',
        'lib/database.ts'
      ];

      const missingLibs = requiredLibs.filter(lib => {
        const libPath = path.join(process.cwd(), lib);
        return !fs.existsSync(libPath);
      });

      if (missingLibs.length > 0) {
        return {
          success: false,
          message: `缺少核心库文件: ${missingLibs.join(', ')}`
        };
      }

      return { success: true, message: '所有核心库文件存在' };
    }
  },
  {
    name: '检查构建',
    check: () => {
      try {
        // 检查是否可以读取 package.json 的构建脚本
        const packagePath = path.join(process.cwd(), 'package.json');
        const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));

        if (pkg.scripts && pkg.scripts.build) {
          return {
            success: true,
            message: '构建脚本配置正确，建议运行 pnpm build 测试构建'
          };
        }

        return { success: false, message: '构建脚本未配置' };
      } catch (error) {
        return { success: false, message: '构建检查失败' };
      }
    }
  }
];

function main() {
  log('🚀 ToolMaster Vercel 部署前检查', 'bold');
  log('=' .repeat(50), 'blue');

  let totalChecks = 0;
  let passedChecks = 0;
  let failedChecks = [];

  checks.forEach(({ name, check }) => {
    totalChecks++;
    log(`\n📋 ${name}`, 'blue');

    try {
      const result = check();

      if (result.success) {
        log(`  ✅ ${result.message}`, 'green');
        passedChecks++;
      } else {
        log(`  ❌ ${result.message}`, 'red');
        failedChecks.push({ name, message: result.message });
      }
    } catch (error) {
      log(`  ❌ 检查失败: ${error.message}`, 'red');
      failedChecks.push({ name, message: error.message });
    }
  });

  // 输出总结
  log('\n' + '=' .repeat(50), 'blue');
  log('📊 检查结果总结:', 'bold');
  log(`总检查项: ${totalChecks}`);
  log(`通过检查: ${passedChecks}`, passedChecks === totalChecks ? 'green' : 'yellow');
  log(`失败检查: ${failedChecks.length}`, failedChecks.length === 0 ? 'green' : 'red');

  if (failedChecks.length > 0) {
    log('\n❌ 以下检查项失败:', 'red');
    failedChecks.forEach(({ name, message }) => {
      log(`  - ${name}: ${message}`, 'red');
    });

    log('\n🔧 修复建议:', 'yellow');
    log('1. 修复上述失败的检查项');
    log('2. 运行 pnpm build 测试构建');
    log('3. 重新运行此检查脚本');
    log('4. 查看部署指南: doc/VERCEL_DEPLOYMENT_GUIDE.md');

    return 1;
  } else {
    log('\n🎉 所有检查通过！项目已准备好部署到 Vercel。', 'green');

    log('\n📝 下一步操作:', 'bold');
    log('1. 推送代码到 GitHub');
    log('2. 在 Vercel 中导入项目');
    log('3. 配置环境变量');
    log('4. 部署项目');
    log('5. 查看详细指南: doc/VERCEL_DEPLOYMENT_GUIDE.md');

    return 0;
  }
}

// 运行检查
if (require.main === module) {
  process.exit(main());
}

module.exports = { main };
