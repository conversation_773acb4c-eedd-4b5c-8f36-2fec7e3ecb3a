# ToolMaster Vercel 部署指南

## 🚀 部署步骤

### 1. **准备工作**

确保您有以下账号和资源：
- ✅ Vercel 账号
- ✅ GitHub 账号（用于代码托管）
- ✅ Supabase 项目（数据库）
- ✅ GLM API 密钥
- ✅ DeepSeek API 密钥（可选，用于深度搜索）

### 2. **推送代码到 GitHub**

```bash
# 如果还没有 Git 仓库，初始化一个
git init
git add .
git commit -m "Initial commit for ToolMaster"

# 创建 GitHub 仓库并推送
git remote add origin https://github.com/YOUR_USERNAME/toolmaster.git
git branch -M main
git push -u origin main
```

### 3. **在 Vercel 中导入项目**

1. 访问 [vercel.com](https://vercel.com)
2. 点击 "New Project"
3. 从 GitHub 导入您的 ToolMaster 仓库
4. 选择 "Next.js" 框架预设
5. 配置项目设置

### 4. **配置环境变量**

在 Vercel 项目设置中添加以下环境变量：

#### **必需的环境变量**

```bash
# AI 服务配置
AI_PROVIDER=glm
GLM_API_KEY=your-glm-api-key-here
GLM_API_URL=https://open.bigmodel.cn/api/paas/v4/chat/completions
GLM_MODEL=glm-4.5-air

# DeepSeek 配置（深度搜索专用）
DEEPSEEK_API_KEY=your-deepseek-api-key-here
DEEPSEEK_API_URL=https://api.deepseek.com/chat/completions
DEEPSEEK_MODEL=deepseek-chat

# 数据库配置
NEXT_PUBLIC_SUPABASE_URL=your-supabase-project-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# 应用配置
ACCESS_PASSWORDS=your-access-password-here
API_ACCESS_KEY=your-api-access-key-here
NEXT_PUBLIC_APP_URL=https://your-vercel-domain.vercel.app

# 可选配置
NODE_ENV=production
```

#### **环境变量说明**

| 变量名 | 说明 | 获取方式 |
|--------|------|----------|
| `GLM_API_KEY` | GLM AI 服务密钥 | [智谱AI开放平台](https://open.bigmodel.cn/) |
| `DEEPSEEK_API_KEY` | DeepSeek AI 服务密钥 | [DeepSeek 平台](https://platform.deepseek.com/) |
| `NEXT_PUBLIC_SUPABASE_URL` | Supabase 项目 URL | Supabase 项目设置 |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Supabase 匿名密钥 | Supabase 项目设置 |
| `ACCESS_PASSWORDS` | 应用访问密码 | 自定义设置 |
| `API_ACCESS_KEY` | API 访问密钥 | 自定义设置 |

### 5. **数据库设置**

确保您的 Supabase 数据库已正确设置：

#### **检查数据库表**

确保以下表存在：
- `tools` - 工具数据表
- `categories` - 分类数据表
- `ai_usage_logs` - AI 使用日志表
- `ai_usage_daily_summary` - AI 使用日统计表（可选）
- `ai_usage_monthly_summary` - AI 使用月统计表（可选）

#### **运行数据库迁移**

如果需要，在 Supabase SQL 编辑器中运行：

```sql
-- 创建 AI 使用日志表
CREATE TABLE IF NOT EXISTS ai_usage_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  api_type TEXT NOT NULL,
  model TEXT NOT NULL,
  input_tokens INTEGER DEFAULT 0,
  output_tokens INTEGER DEFAULT 0,
  total_tokens INTEGER DEFAULT 0,
  cost_yuan DECIMAL(10,6) DEFAULT 0,
  response_time_ms INTEGER DEFAULT 0,
  success BOOLEAN DEFAULT true,
  error_message TEXT,
  request_id TEXT,
  user_query TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_created_at ON ai_usage_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_model ON ai_usage_logs(model);
CREATE INDEX IF NOT EXISTS idx_ai_usage_logs_api_type ON ai_usage_logs(api_type);
```

### 6. **部署配置优化**

#### **Vercel 函数配置**

项目已配置以下 Vercel 设置：

```json
{
  "functions": {
    "app/api/backup/route.ts": {
      "maxDuration": 60
    },
    "app/api/*/route.ts": {
      "maxDuration": 30
    }
  },
  "regions": ["hkg1", "sin1", "nrt1"]
}
```

#### **性能优化**

- ✅ 使用亚洲区域部署（香港、新加坡、东京）
- ✅ API 函数超时设置为 30 秒
- ✅ 备份函数超时设置为 60 秒
- ✅ 图片优化已启用

### 7. **部署验证**

部署完成后，验证以下功能：

#### **基础功能测试**
- [ ] 应用首页正常加载
- [ ] 工具搜索功能正常
- [ ] 分类筛选功能正常
- [ ] 数据管理中心正常

#### **AI 功能测试**
- [ ] 快速添加 - URL 分析
- [ ] 添加工具功能
- [ ] 深度搜索功能
- [ ] 全网搜索功能
- [ ] 文本导入功能
- [ ] 文件导入功能

#### **监控功能测试**
- [ ] AI 使用统计正常显示
- [ ] 费用计算准确
- [ ] 使用趋势图表正常

### 8. **域名配置（可选）**

如果您有自定义域名：

1. 在 Vercel 项目设置中添加域名
2. 配置 DNS 记录指向 Vercel
3. 更新 `NEXT_PUBLIC_APP_URL` 环境变量

### 9. **监控和维护**

#### **Vercel 监控**
- 查看部署日志
- 监控函数执行时间
- 查看错误报告

#### **应用监控**
- 定期检查 AI 使用统计
- 监控 API 调用成功率
- 查看数据库使用情况

## 🔧 常见问题解决

### **部署失败**

1. **构建错误**：
   ```bash
   # 本地测试构建
   pnpm build
   ```

2. **环境变量错误**：
   - 检查所有必需的环境变量是否已设置
   - 确认 API 密钥格式正确

3. **数据库连接错误**：
   - 验证 Supabase 配置
   - 检查数据库表是否存在

### **功能异常**

1. **AI 功能不工作**：
   - 检查 AI API 密钥是否有效
   - 查看 Vercel 函数日志

2. **数据库操作失败**：
   - 检查 Supabase 权限设置
   - 验证 RLS 策略配置

3. **性能问题**：
   - 检查函数执行时间
   - 优化数据库查询

## 📞 技术支持

如果遇到部署问题：

1. 查看 Vercel 部署日志
2. 检查浏览器控制台错误
3. 验证环境变量配置
4. 测试数据库连接

## 🎯 部署检查清单

- [ ] GitHub 仓库已创建并推送代码
- [ ] Vercel 项目已创建
- [ ] 所有环境变量已配置
- [ ] Supabase 数据库已设置
- [ ] AI API 密钥已获取并配置
- [ ] 应用功能测试通过
- [ ] 监控系统正常工作

完成以上步骤后，您的 ToolMaster 应用就成功部署到 Vercel 了！🎉
