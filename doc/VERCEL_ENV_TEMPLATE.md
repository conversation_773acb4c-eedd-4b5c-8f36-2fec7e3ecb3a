# Vercel 环境变量配置模板

## 🔑 必需的环境变量

请在 Vercel 项目设置 → Environment Variables 中添加以下变量：

### **AI 服务配置**

```bash
# 主要 AI 提供商
AI_PROVIDER=glm

# GLM-4.5-air 配置
GLM_API_KEY=your-glm-api-key-here
GLM_API_URL=https://open.bigmodel.cn/api/paas/v4/chat/completions
GLM_MODEL=glm-4.5-air

# DeepSeek 配置（深度搜索专用）
DEEPSEEK_API_KEY=your-deepseek-api-key-here
DEEPSEEK_API_URL=https://api.deepseek.com/chat/completions
DEEPSEEK_MODEL=deepseek-chat
```

### **数据库配置**

```bash
# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
```

### **应用安全配置**

```bash
# 访问控制
ACCESS_PASSWORDS=your-secure-password-here
API_ACCESS_KEY=your-api-access-key-here

# 应用 URL
NEXT_PUBLIC_APP_URL=https://your-vercel-domain.vercel.app
```

### **系统配置**

```bash
# 环境
NODE_ENV=production

# 时区（可选）
TZ=Asia/Shanghai
```

## 📋 环境变量获取指南

### **1. GLM API 密钥**

1. 访问 [智谱AI开放平台](https://open.bigmodel.cn/)
2. 注册并完成实名认证
3. 创建应用获取 API Key
4. 复制 API Key 到 `GLM_API_KEY`

### **2. DeepSeek API 密钥**

1. 访问 [DeepSeek 平台](https://platform.deepseek.com/)
2. 注册账号
3. 创建 API Key
4. 复制 API Key 到 `DEEPSEEK_API_KEY`

### **3. Supabase 配置**

1. 访问 [Supabase](https://supabase.com/)
2. 创建新项目
3. 在项目设置 → API 中找到：
   - Project URL → `NEXT_PUBLIC_SUPABASE_URL`
   - anon public → `NEXT_PUBLIC_SUPABASE_ANON_KEY`

### **4. 自定义配置**

```bash
# 访问密码（自定义）
ACCESS_PASSWORDS=mySecurePassword123

# API 访问密钥（自定义，建议使用随机字符串）
API_ACCESS_KEY=sk-1234567890abcdef

# 应用 URL（部署后获取）
NEXT_PUBLIC_APP_URL=https://toolmaster-xxx.vercel.app
```

## ⚠️ 安全注意事项

1. **不要在代码中硬编码密钥**
2. **使用强密码和随机 API 密钥**
3. **定期轮换密钥**
4. **限制 API 密钥权限**

## 🔧 配置验证

部署后，访问以下 URL 验证配置：

```bash
# 检查应用状态
https://your-domain.vercel.app/

# 检查 API 状态（需要 API 密钥）
https://your-domain.vercel.app/api/ai-usage-stats?type=today
```

## 📞 获取帮助

如果配置过程中遇到问题：

1. 检查环境变量名称是否正确
2. 确认 API 密钥格式正确
3. 验证 Supabase 项目设置
4. 查看 Vercel 部署日志
