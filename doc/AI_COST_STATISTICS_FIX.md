# AI费用统计修复报告

## 🎯 问题概述

在数据管理页面的AI费用统计中发现多个显示问题：

1. **日均费用显示错误**：显示为"¥"而没有具体数值
2. **字段缺失**：多个前端组件期望的字段在后端统计中不存在
3. **字段名不一致**：前后端使用不同的字段名称
4. **功能缺失**：缺少API类型分布统计和使用趋势

## 🔍 问题详细分析

### 1. **日均费用计算缺失**

**问题**：前端期望 `avg_daily_cost_yuan` 字段，但后端没有提供
```javascript
// 前端代码
<div>¥{aiUsageMonthly.avg_daily_cost_yuan?.toFixed(4)}</div>
```

**原因**：月度统计方法中没有计算日均费用

### 2. **今日统计字段缺失**

**问题**：前端期望多个字段，但后端没有提供
```javascript
// 前端期望的字段
total_input_tokens      // 输入tokens
total_output_tokens     // 输出tokens  
discount_cost_yuan      // 优惠费用
max_response_time_ms    // 最大响应时间
api_type_stats          // API类型统计
```

### 3. **字段名不一致**

**问题**：前后端使用不同的字段名
```javascript
// 后端返回: date
// 前端期望: date_only

// 使用趋势中的字段名不匹配
```

### 4. **API类型分布统计缺失**

**问题**：前端期望显示各功能的使用分布，但后端没有提供
```javascript
// 前端期望的结构
api_type_stats: {
  "deep-search": { calls: 10, cost: 0.05, tokens: 1000 },
  "analyze-url": { calls: 20, cost: 0.03, tokens: 800 }
}
```

### 5. **使用趋势数据源问题**

**问题**：使用趋势方法还在查询不存在的数据库表
```javascript
// 错误的查询
.from('ai_usage_daily_summary')  // 表不存在
```

## 🛠️ 修复方案

### 1. **添加日均费用计算**

在月度统计中添加日均费用计算：

```javascript
// 计算日均费用
const currentDate = new Date()
const currentDay = currentDate.getDate()
const avgDailyCost = totalCalls > 0 ? totalCost / currentDay : 0

return {
  // ... 其他字段
  avg_daily_cost_yuan: Number(avgDailyCost.toFixed(4))
}
```

### 2. **完善今日统计字段**

添加所有前端需要的字段：

```javascript
// 新增统计变量
let totalInputTokens = 0
let totalOutputTokens = 0
let maxResponseTime = 0
const apiTypeStats: { [key: string]: any } = {}

// 在循环中累计统计
logs?.forEach(log => {
  // 模型统计
  // ...
  
  // API类型统计
  const apiType = log.api_type || 'unknown'
  if (!apiTypeStats[apiType]) {
    apiTypeStats[apiType] = { calls: 0, cost: 0, tokens: 0 }
  }
  apiTypeStats[apiType].calls++
  apiTypeStats[apiType].cost += log.cost_yuan || 0
  apiTypeStats[apiType].tokens += log.total_tokens || 0
  
  // 其他统计
  totalInputTokens += log.input_tokens || 0
  totalOutputTokens += log.output_tokens || 0
  maxResponseTime = Math.max(maxResponseTime, log.response_time_ms || 0)
})

return {
  // ... 原有字段
  total_input_tokens: totalInputTokens,
  total_output_tokens: totalOutputTokens,
  max_response_time_ms: maxResponseTime,
  discount_cost_yuan: 0, // 暂时设为0
  api_type_stats: apiTypeStats,
  date_only: today // 兼容前端
}
```

### 3. **修复使用趋势数据源**

将使用趋势改为从原始日志实时计算：

```javascript
static async getRecentUsageTrend(days: number = 7): Promise<any[]> {
  const trendData = []
  const today = new Date()

  for (let i = 0; i < days; i++) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    const dateStr = date.toISOString().split('T')[0]

    // 获取该日期的使用记录
    const { data: logs } = await supabase
      .from('ai_usage_logs')
      .select('*')
      .gte('created_at', `${dateStr}T00:00:00.000Z`)
      .lt('created_at', `${dateStr}T23:59:59.999Z`)

    // 计算该日统计
    let totalCalls = 0
    let totalCost = 0
    let totalTokens = 0

    logs?.forEach(log => {
      totalCalls++
      totalCost += log.cost_yuan || 0
      totalTokens += log.total_tokens || 0
    })

    trendData.push({
      date_only: dateStr,
      total_calls: totalCalls,
      total_cost_yuan: Number(totalCost.toFixed(4)),
      total_tokens: totalTokens
    })
  }

  return trendData
}
```

## 📊 修复效果对比

### **日均费用显示**

| 修复前 | 修复后 |
|--------|--------|
| ❌ 显示"¥"无数值 | ✅ 显示"¥0.0034"等具体数值 |
| ❌ 字段不存在 | ✅ 正确计算日均费用 |

### **今日统计完整性**

| 字段 | 修复前 | 修复后 |
|------|--------|--------|
| total_input_tokens | ❌ 缺失 | ✅ 正确统计 |
| total_output_tokens | ❌ 缺失 | ✅ 正确统计 |
| max_response_time_ms | ❌ 缺失 | ✅ 正确统计 |
| api_type_stats | ❌ 缺失 | ✅ 按功能分组统计 |
| discount_cost_yuan | ❌ 缺失 | ✅ 预留字段 |

### **API类型分布统计**

| 修复前 | 修复后 |
|--------|--------|
| ❌ 不显示API分布 | ✅ 显示各功能使用情况 |
| ❌ 无法了解功能使用 | ✅ 清楚显示深度搜索、URL分析等使用量 |

### **使用趋势**

| 修复前 | 修复后 |
|--------|--------|
| ❌ 查询不存在的表 | ✅ 从原始日志实时计算 |
| ❌ 可能无数据显示 | ✅ 显示最近7天趋势 |

## 🧪 测试验证

### 1. **启动应用测试**

```bash
pnpm dev
```

### 2. **访问数据管理页面**

打开数据管理中心 → AI费用标签页

**验证项目**：
- ✅ 日均费用显示具体数值
- ✅ 今日统计显示完整信息
- ✅ API使用分布正常显示
- ✅ 7日使用趋势正常显示

### 3. **API接口测试**

```bash
# 测试今日统计
curl http://localhost:3000/api/ai-usage-stats?type=today

# 测试月度统计
curl http://localhost:3000/api/ai-usage-stats?type=monthly

# 测试使用趋势
curl http://localhost:3000/api/ai-usage-stats?type=trend&days=7
```

**预期返回结构**：

```json
// 今日统计
{
  "success": true,
  "data": {
    "date": "2025-01-08",
    "date_only": "2025-01-08",
    "total_calls": 25,
    "total_cost_yuan": 0.0342,
    "total_input_tokens": 4200,
    "total_output_tokens": 2080,
    "max_response_time_ms": 5361,
    "api_type_stats": {
      "analyze-url": { "calls": 15, "cost": 0.0200, "tokens": 3000 },
      "deep-search": { "calls": 10, "cost": 0.0142, "tokens": 3280 }
    },
    "model_breakdown": [...]
  }
}

// 月度统计
{
  "success": true,
  "data": {
    "month": "2025-01",
    "total_calls": 150,
    "total_cost_yuan": 0.2050,
    "avg_daily_cost_yuan": 0.0256,
    "model_breakdown": [...]
  }
}
```

## 📋 修改文件清单

### 🔄 **修改的文件**

1. **`lib/ai-usage-monitor.ts`**
   - ✅ 添加日均费用计算
   - ✅ 完善今日统计字段
   - ✅ 添加API类型分布统计
   - ✅ 修复使用趋势数据源
   - ✅ 统一字段命名

2. **新增文档**
   - ✅ `doc/AI_COST_STATISTICS_FIX.md` - 本修复报告

### ✅ **验证通过的功能**

- ✅ 日均费用正确显示
- ✅ 今日统计字段完整
- ✅ API类型分布统计正常
- ✅ 使用趋势数据正确
- ✅ 字段名称统一

## 🎯 数据结构优化

### **统计数据完整性**

现在AI费用统计提供完整的数据结构：

```javascript
// 今日统计
{
  基础统计: {
    total_calls,           // 总调用次数
    success_calls,         // 成功调用次数
    success_rate          // 成功率
  },
  费用统计: {
    total_cost_yuan,      // 总费用
    discount_cost_yuan    // 优惠费用
  },
  Token统计: {
    total_tokens,         // 总tokens
    total_input_tokens,   // 输入tokens
    total_output_tokens   // 输出tokens
  },
  性能统计: {
    avg_response_time_ms, // 平均响应时间
    max_response_time_ms  // 最大响应时间
  },
  分组统计: {
    model_breakdown,      // 按模型分组
    api_type_stats       // 按API类型分组
  }
}

// 月度统计
{
  基础统计: { ... },
  费用统计: {
    total_cost_yuan,      // 总费用
    avg_daily_cost_yuan   // 日均费用
  },
  分组统计: { ... }
}
```

## 📝 总结

通过本次修复，我们成功解决了AI费用统计页面的所有显示问题：

✅ **日均费用正确计算和显示**  
✅ **今日统计字段完整性**  
✅ **API类型分布统计功能**  
✅ **使用趋势数据准确性**  
✅ **字段命名一致性**  

现在AI费用统计页面能够：
- 准确显示日均费用
- 完整展示各项统计指标
- 清楚显示不同功能的使用分布
- 正确展示最近7天的使用趋势
- 按AI模型分别统计费用

用户可以清楚地了解AI服务的使用情况和费用分布，有助于成本控制和使用优化！🎉
