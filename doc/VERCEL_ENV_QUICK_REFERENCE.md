# Vercel 环境变量快速参考

## 🔑 正确的环境变量名称

**重要提醒**：请使用以下正确的环境变量名称，特别注意 Supabase 配置使用 `NEXT_PUBLIC_` 前缀。

### **在 Vercel 项目设置中配置以下环境变量：**

```bash
# AI 服务配置
AI_PROVIDER=glm
GLM_API_KEY=your-glm-api-key-here
GLM_API_URL=https://open.bigmodel.cn/api/paas/v4/chat/completions
GLM_MODEL=glm-4.5-air
DEEPSEEK_API_KEY=your-deepseek-api-key-here
DEEPSEEK_API_URL=https://api.deepseek.com/chat/completions
DEEPSEEK_MODEL=deepseek-chat

# 数据库配置（注意使用 NEXT_PUBLIC_ 前缀）
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# 应用安全配置
ACCESS_PASSWORDS=your-secure-password-here
API_ACCESS_KEY=your-api-access-key-here
NEXT_PUBLIC_APP_URL=https://your-vercel-domain.vercel.app

# 系统配置
NODE_ENV=production
```

## ⚠️ 常见错误

### **错误的配置**：
```bash
❌ SUPABASE_URL=...
❌ SUPABASE_ANON_KEY=...
❌ SUPABASE_SERVICE_ROLE_KEY=...
```

### **正确的配置**：
```bash
✅ NEXT_PUBLIC_SUPABASE_URL=...
✅ NEXT_PUBLIC_SUPABASE_ANON_KEY=...
```

## 📋 为什么使用 NEXT_PUBLIC_ 前缀？

1. **客户端访问**：Supabase 客户端需要在浏览器中访问这些变量
2. **Next.js 要求**：客户端可访问的环境变量必须以 `NEXT_PUBLIC_` 开头
3. **安全考虑**：匿名密钥是公开的，可以安全地暴露给客户端

## 🔧 配置步骤

1. **登录 Vercel**
2. **进入项目设置**
3. **点击 Environment Variables**
4. **逐一添加上述环境变量**
5. **确保变量名完全正确**
6. **重新部署项目**

## 📞 验证配置

部署后访问：`https://your-domain.vercel.app/`

如果看到应用正常加载，说明配置正确！
