# GLM-4.5-air 配置指南

## 🚀 快速开始

### 1. 获取 GLM API 密钥

1. 访问 [智谱AI开放平台](https://open.bigmodel.cn/)
2. 注册账号并完成实名认证
3. 创建应用并获取 API Key
4. 记录您的 API Key (格式类似: `3f87d203ae174edfb5a7708209480987.fLPyOfpf75m2c9fv`)

### 2. 配置环境变量

#### 方法一: 使用 .env.local 文件 (推荐)

在项目根目录创建 `.env.local` 文件:

```bash
# AI 服务配置
AI_PROVIDER=glm
GLM_API_KEY=your-glm-api-key-here

# 可选配置 (使用默认值即可)
GLM_API_URL=https://open.bigmodel.cn/api/paas/v4/chat/completions
GLM_MODEL=glm-4.5-air

# 保留 DeepSeek 配置作为备用 (可选)
DEEPSEEK_API_KEY=your-deepseek-api-key-here
DEEPSEEK_API_URL=https://api.deepseek.com/chat/completions
DEEPSEEK_MODEL=deepseek-chat

# 其他必要配置
ACCESS_PASSWORDS=your-access-password
API_ACCESS_KEY=your-api-access-key
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-supabase-anon-key
```

#### 方法二: 系统环境变量

```bash
export AI_PROVIDER="glm"
export GLM_API_KEY="your-glm-api-key-here"
```

### 3. 启动应用

```bash
# 安装依赖
pnpm install

# 开发模式
pnpm dev

# 或生产模式
pnpm build
pnpm start
```

### 4. 验证配置

访问测试页面验证配置是否正确:
```
http://localhost:3000/test/test-ai-provider-switch.html
```

## 🔧 高级配置

### 动态切换 AI 提供商

无需重启应用，只需修改环境变量:

```bash
# 切换到 GLM
export AI_PROVIDER="glm"

# 切换到 DeepSeek
export AI_PROVIDER="deepseek"
```

### 自定义 API 配置

```bash
# 自定义 GLM API 地址 (通常不需要修改)
export GLM_API_URL="https://open.bigmodel.cn/api/paas/v4/chat/completions"

# 自定义模型名称 (通常不需要修改)
export GLM_MODEL="glm-4.5-air"
```

## 📊 费用说明

### GLM-4.5-air 定价

| 类型 | 价格 | 说明 |
|------|------|------|
| 输入 tokens | ¥0.5/百万 | 用户输入和系统提示 |
| 输出 tokens | ¥1.5/百万 | AI 生成的回复内容 |

### 费用估算

基于典型使用场景:

| 功能 | 平均输入 | 平均输出 | 单次费用 | 1000次费用 |
|------|----------|----------|----------|------------|
| URL 分析 | 800 tokens | 400 tokens | ¥0.001 | ¥1.0 |
| 深度搜索 | 1500 tokens | 800 tokens | ¥0.002 | ¥2.0 |
| 全网搜索 | 2000 tokens | 1000 tokens | ¥0.003 | ¥3.0 |

**预计月费用**: 基于中等使用量，约 ¥10-50/月

## 🧪 测试验证

### 自动化验证

```bash
# 验证所有修改是否正确
node test/verify-ai-provider-changes.js
```

### 功能测试

1. **URL 分析测试**:
   ```bash
   curl -X POST http://localhost:3000/api/analyze-url \
     -H "Content-Type: application/json" \
     -H "X-API-Key: your-api-key" \
     -d '{"url": "https://github.com"}'
   ```

2. **深度搜索测试**:
   ```bash
   curl -X POST http://localhost:3000/api/deep-search \
     -H "Content-Type: application/json" \
     -H "X-API-Key: your-api-key" \
     -d '{"query": "代码编辑器", "useOptimized": true}'
   ```

### 性能测试

访问测试页面进行交互式测试:
```
http://localhost:3000/test/test-ai-provider-switch.html
```

## 📈 监控和优化

### 使用统计

在应用中查看 AI 使用统计:
1. 打开数据管理面板
2. 查看 "AI 使用统计" 部分
3. 监控费用和性能指标

### API 监控

```bash
# 获取今日使用统计
curl http://localhost:3000/api/ai-usage-stats?type=today

# 获取月度使用统计
curl http://localhost:3000/api/ai-usage-stats?type=monthly
```

## 🔧 故障排除

### 常见问题

**Q1: API 调用失败，返回 401 错误**
```
A: 检查 GLM_API_KEY 是否正确设置，确保 API Key 有效且未过期
```

**Q2: 应用启动后仍使用 DeepSeek**
```
A: 确认 AI_PROVIDER=glm 环境变量已设置，重启应用服务器
```

**Q3: GLM API 响应慢或超时**
```
A: 检查网络连接，可临时切换到 DeepSeek: AI_PROVIDER=deepseek
```

**Q4: 费用计算不准确**
```
A: 检查 lib/ai-usage-monitor.ts 中的价格配置是否最新
```

### 调试模式

启用详细日志:
```bash
export DEBUG=ai-service
pnpm dev
```

### 回滚方案

如需回滚到 DeepSeek:
```bash
export AI_PROVIDER="deepseek"
# 重启应用
```

## 📞 技术支持

### 获取帮助

1. **查看日志**: 检查应用控制台输出
2. **测试页面**: 使用 `/test/test-ai-provider-switch.html` 诊断
3. **验证脚本**: 运行 `node test/verify-ai-provider-changes.js`

### 联系方式

- **智谱AI支持**: [开放平台文档](https://docs.bigmodel.cn/)
- **项目问题**: 查看项目 Issues 或创建新 Issue

## 🎯 最佳实践

### 1. 环境管理
- 开发环境使用 `.env.local`
- 生产环境使用系统环境变量
- 定期备份环境配置

### 2. 费用控制
- 设置合理的 `max_tokens` 限制
- 监控日常使用量
- 使用缓存减少重复调用

### 3. 性能优化
- 优先使用 `useOptimized=true` 参数
- 合理设置超时时间
- 监控响应时间指标

### 4. 安全考虑
- 妥善保管 API Key
- 定期轮换密钥
- 使用 API 访问控制

---

## ✅ 配置检查清单

- [ ] 获取 GLM API Key
- [ ] 设置环境变量 `AI_PROVIDER=glm`
- [ ] 设置环境变量 `GLM_API_KEY`
- [ ] 运行验证脚本
- [ ] 测试 URL 分析功能
- [ ] 测试深度搜索功能
- [ ] 检查费用统计
- [ ] 配置监控告警

完成以上步骤后，您的 ToolMaster 应用就成功切换到 GLM-4.5-air 了！🎉
