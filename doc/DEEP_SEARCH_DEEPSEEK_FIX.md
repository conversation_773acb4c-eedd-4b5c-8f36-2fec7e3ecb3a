# 深度搜索强制使用DeepSeek修复报告

## 🎯 问题背景

在将主要AI服务切换到GLM-4.5-air后，深度搜索功能频繁触发GLM的敏感内容检测，返回1301错误：

```
GLM API error: 400 - {
  "contentFilter": [{"level": 1, "role": "user"}],
  "error": {
    "code": "1301", 
    "message": "System detected potentially unsafe or sensitive content in input or generation."
  }
}
```

## 🔍 根本原因分析

### 1. **GLM内容安全检测过于严格**
- GLM对某些查询词汇（如"在线观看"）非常敏感
- 工具库中包含视频平台（如哔哩哔哩）可能触发检测
- 即使是正常的工具推荐也可能被误判为敏感内容

### 2. **DeepSeek相对宽松**
- DeepSeek的内容检测相对宽松，很少出现误判
- 对工具推荐类查询处理更加友好
- 深度搜索功能在DeepSeek上运行稳定

## 🛠️ 解决方案

### **策略：深度搜索专用DeepSeek**

将深度搜索功能强制使用DeepSeek，而其他功能继续使用GLM：

```
┌─────────────────────────────────────────────────────────────┐
│                    AI服务分工策略                            │
├─────────────────────────────────────────────────────────────┤
│  功能模块          │  AI提供商    │  原因                    │
├─────────────────────────────────────────────────────────────┤
│  URL分析          │  GLM        │  速度快，成本低           │
│  文本/文件导入     │  GLM        │  批量处理效率高           │
│  全网搜索         │  GLM        │  响应速度快               │
│  深度搜索         │  DeepSeek   │  避免敏感内容检测         │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 技术实现

### 1. **添加DeepSeek专用API调用方法**

#### **标准版AI服务** (`lib/ai-service.ts`)

```javascript
// 新增DeepSeek专用方法
private static async callDeepSeekAPI(
  systemPrompt: string,
  userPrompt: string,
  temperature: number = 0.3,
  maxTokens: number = 1000,
  apiType: string = 'deep-search',
  requestId?: string
): Promise<string> {
  // 强制使用DeepSeek配置
  const deepseekApiKey = process.env.DEEPSEEK_API_KEY
  const deepseekApiUrl = process.env.DEEPSEEK_API_URL || 'https://api.deepseek.com/chat/completions'
  
  // 直接调用DeepSeek API
  const response = await fetch(deepseekApiUrl, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${deepseekApiKey}`
    },
    body: JSON.stringify({
      model: 'deepseek-chat',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature,
      max_tokens: maxTokens,
      stream: false
    })
  })
  
  // 处理响应...
}
```

#### **优化版AI服务** (`lib/ai-service-optimized.ts`)

```javascript
// 新增DeepSeek专用重试方法
private static async callDeepSeekAPIWithRetry(
  systemPrompt: string,
  userPrompt: string,
  temperature: number = 0.3,
  maxTokens: number = 1000,
  timeout: number = 30000,
  maxRetries: number = 2
): Promise<string> {
  // 带重试机制的DeepSeek调用
  for (let attempt = 1; attempt <= maxRetries + 1; attempt++) {
    try {
      return await this.callDeepSeekAPI(systemPrompt, userPrompt, temperature, maxTokens, timeout)
    } catch (error) {
      if (attempt <= maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000)
        await new Promise(resolve => setTimeout(resolve, delay))
      } else {
        throw error
      }
    }
  }
}
```

### 2. **修改深度搜索调用**

#### **标准深度搜索**
```javascript
// 修改前
const aiResponse = await this.callAIAPI(systemPrompt, userPrompt, 0.7, 2000, 'deep-search', requestId)

// 修改后
const aiResponse = await this.callDeepSeekAPI(systemPrompt, userPrompt, 0.7, 2000, 'deep-search', requestId)
```

#### **快速深度搜索**
```javascript
// 修改前
const aiResponse = await this.callAIAPIWithRetry(...)

// 修改后  
const aiResponse = await this.callDeepSeekAPIWithRetry(...)
```

### 3. **修复数据库字段错误**

修复AI使用统计中的字段名错误：

```javascript
// 修改前
return {
  date_only: today,        // ❌ 错误字段名
  year_month: yearMonth    // ❌ 错误字段名
}

// 修改后
return {
  date: today,             // ✅ 正确字段名
  month: yearMonth         // ✅ 正确字段名
}
```

## 📊 修复效果

### **深度搜索功能对比**

| 修复前 | 修复后 |
|--------|--------|
| ❌ GLM敏感内容检测频繁报错 | ✅ DeepSeek稳定运行 |
| ❌ 查询"在线观看"等词汇失败 | ✅ 正常处理各类查询 |
| ❌ 用户体验差，功能不可用 | ✅ 功能完全正常 |

### **AI服务使用分工**

| 功能 | AI提供商 | 优势 | 状态 |
|------|----------|------|------|
| URL分析 | GLM-4.5-air | 速度快，成本低 | ✅ 正常 |
| 文本导入 | GLM-4.5-air | 批量处理效率高 | ✅ 正常 |
| 文件导入 | GLM-4.5-air | 批量处理效率高 | ✅ 正常 |
| 全网搜索 | GLM-4.5-air | 响应速度快 | ✅ 正常 |
| **深度搜索** | **DeepSeek** | **避免敏感内容检测** | ✅ **修复** |

### **费用统计修复**

| 修复前 | 修复后 |
|--------|--------|
| ❌ 数据库字段错误 | ✅ 字段名正确 |
| ❌ 统计数据无法显示 | ✅ 正常显示统计 |
| ❌ GLM和DeepSeek费用混淆 | ✅ 按模型分别统计 |

## 🧪 测试验证

### 1. **深度搜索测试**

**测试用例**：
- ✅ "最好用的在线观看" - 之前触发1301错误，现在正常
- ✅ "视频播放工具" - 正常推荐相关工具
- ✅ "社区论坛" - 正常推荐社交平台
- ✅ "代码编辑器" - 正常推荐开发工具

**测试方法**：
```bash
curl -X POST http://localhost:3000/api/deep-search \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your-api-key" \
  -d '{"query": "最好用的在线观看", "useOptimized": true}'
```

### 2. **费用统计测试**

**测试方法**：
```bash
# 获取今日统计
curl http://localhost:3000/api/ai-usage-stats?type=today

# 获取月度统计  
curl http://localhost:3000/api/ai-usage-stats?type=monthly
```

**预期结果**：
- 显示GLM和DeepSeek的分别统计
- 深度搜索使用DeepSeek模型记录
- 其他功能使用GLM模型记录

## 📋 修改文件清单

### 🔄 **修改的文件**

1. **`lib/ai-service.ts`**
   - ✅ 新增 `callDeepSeekAPI()` 方法
   - ✅ 修改深度搜索使用DeepSeek

2. **`lib/ai-service-optimized.ts`**
   - ✅ 新增 `callDeepSeekAPIWithRetry()` 方法
   - ✅ 新增 `callDeepSeekAPI()` 方法
   - ✅ 修改快速深度搜索使用DeepSeek

3. **`lib/ai-usage-monitor.ts`**
   - ✅ 修复数据库字段名错误
   - ✅ 统计方法返回正确字段名

4. **新增文档**
   - ✅ `doc/DEEP_SEARCH_DEEPSEEK_FIX.md` - 本修复报告

## 🚀 使用指南

### 1. **环境配置要求**

确保同时配置GLM和DeepSeek的API密钥：

```bash
# GLM配置（主要AI服务）
export AI_PROVIDER="glm"
export GLM_API_KEY="your-glm-api-key"

# DeepSeek配置（深度搜索专用）
export DEEPSEEK_API_KEY="your-deepseek-api-key"
export DEEPSEEK_API_URL="https://api.deepseek.com/chat/completions"
```

### 2. **功能验证**

启动应用后测试各功能：

```bash
pnpm dev
```

- ✅ **URL分析**: 使用GLM，速度快
- ✅ **文本/文件导入**: 使用GLM，效率高  
- ✅ **全网搜索**: 使用GLM，响应快
- ✅ **深度搜索**: 使用DeepSeek，稳定可靠

### 3. **监控使用情况**

在数据管理页面查看AI使用统计：
- GLM使用情况：URL分析、导入、全网搜索
- DeepSeek使用情况：深度搜索
- 费用分别统计，清晰明了

## 🎯 优势总结

### **技术优势**
1. ✅ **功能稳定**: 深度搜索不再受GLM敏感内容检测影响
2. ✅ **成本优化**: 大部分功能使用更便宜的GLM
3. ✅ **性能保证**: 各功能使用最适合的AI模型
4. ✅ **向后兼容**: 不影响现有功能和数据

### **用户体验**
1. ✅ **功能完整**: 所有AI功能正常可用
2. ✅ **响应稳定**: 深度搜索不再出现错误
3. ✅ **查询自由**: 支持各种类型的搜索查询
4. ✅ **费用透明**: 清楚显示各AI模型的使用情况

## 📝 总结

通过将深度搜索功能强制使用DeepSeek，我们成功解决了GLM敏感内容检测的问题，同时保持了其他功能使用GLM的成本和性能优势。

这种**混合AI策略**既解决了技术问题，又优化了成本结构，为用户提供了稳定可靠的AI功能体验。

现在所有AI功能都能正常工作，深度搜索功能完全恢复，用户可以自由进行各种查询而不用担心敏感内容检测的问题！🎉
