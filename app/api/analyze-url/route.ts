import { NextRequest, NextResponse } from "next/server"
import { validateApiAccess } from "@/lib/api-auth"
import { AIServiceFactory, AIMessage } from "@/lib/ai-providers"
import { AIUsageMonitor } from "@/lib/ai-usage-monitor"

export const runtime = "edge" // Use the edge runtime for faster responses

export async function POST(req: NextRequest) {
  // 验证API访问权限
  const authError = validateApiAccess(req)
  if (authError) {
    return authError
  }
  try {
    const { url } = await req.json()

    if (!url) {
      return NextResponse.json({ error: "URL is required" }, { status: 400 })
    }

    // Step 1: Fetch the content of the URL
    let pageContent = ""
    try {
      const response = await fetch(url)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      pageContent = await response.text()
    } catch (fetchError) {
      console.error("Failed to fetch URL content:", fetchError)
      // If fetching fails, proceed with just the URL
      pageContent = `Could not fetch content from ${url}. Please analyze based on the URL itself.`
    }

    // Extract a relevant snippet if the content is too long
    const snippet = pageContent.slice(0, 5000) // Limit to first 5000 characters

    // Step 2: Use AI to analyze the content and extract information
    const systemPrompt = `你是一个专业的网站分析和工具分类专家。请仔细分析用户提供的网站URL和页面内容，准确识别网站的真实身份和功能，然后生成详细的分析结果。

## 分析要求：

1. **工具名称**：
   - 提取网站的真实中文名称或官方名称
   - 如果是英文网站，提供准确的中文翻译
   - 例如：GitHub → GitHub、Figma → Figma、Calculator.net → 在线计算器

2. **工具描述**：
   - 生成80-150字的详细描述
   - 必须包含工具的主要功能、特色功能、适用场景
   - 描述要具体、准确、有吸引力
   - 避免空泛的描述

3. **相关标签**：
   - 必须生成4-8个相关的中文标签
   - 标签要具体、准确、有搜索价值
   - 包含功能标签、行业标签、技术标签等

4. **分类信息**：
   - 仔细选择最合适的三级分类
   - 优先选择具体的功能分类，避免选择"其他"类别
   - 如果不确定，选择最接近的分类

## 完整分类结构：

**development (开发工具)**
- code-editor (代码编辑): online-editor, ide, text-editor, code-formatter
- version-control (版本控制): git-tools, collaboration, code-review
- api-tools (API工具): api-testing, api-docs, mock-tools
- database (数据库工具): db-client, db-design, db-migration
- deployment (部署运维): ci-cd, monitoring, container

**design (设计工具)**
- ui-design (UI设计): prototyping, wireframe, design-system
- graphics (图形设计): vector-graphics, photo-editing, illustration
- color-tools (色彩工具): color-picker, color-palette, gradient
- icon-fonts (图标字体): icon-library, font-tools, emoji
- 3d-design (3D设计): 3d-modeling, 3d-rendering, 3d-animation

**productivity (效率工具)**
- note-taking (笔记工具): markdown, knowledge-base, mind-map
- task-management (任务管理): todo-list, project-management, time-tracking
- automation (自动化工具): workflow, scripting, integration
- office-tools (办公工具): document, spreadsheet, presentation

**learning (学习资源)**
- programming (编程学习): coding-practice, algorithm, tutorial
- language (语言学习): vocabulary, grammar, pronunciation
- reference (参考资料): documentation, cheatsheet, examples
- online-courses (在线课程): mooc, video-courses, certification

**entertainment (娱乐工具)**
- media-streaming (媒体播放): video-streaming, music-streaming, podcast
- games (游戏娱乐): online-games, game-tools, game-community
- content-download (内容下载): video-download, music-download, movie-download
- social-entertainment (社交娱乐): social-media, chat-tools, community

**life-service (生活服务)**
- daily-tools (日常工具): weather, calendar, reminder
- travel (旅行出行): map-navigation, booking, travel-guide
- health-fitness (健康健身): fitness-tracker, health-monitor, nutrition
- finance (金融理财): budget-tracker, investment, currency

**business (商业工具)**
- marketing (营销推广): seo-tools, social-marketing, email-marketing
- analytics (数据分析): web-analytics, business-intelligence, data-visualization
- customer-service (客户服务): live-chat, help-desk, feedback
- e-commerce (电子商务): online-store, payment, inventory

**system (系统工具)**
- network-tools (网络工具): speed-test, dns-tools, proxy-vpn
- security (安全工具): password-manager, encryption, security-scan
- file-tools (文件工具): file-converter, file-compress, file-recovery
- system-monitor (系统监控): performance, resource-usage, system-info

**ai-tools (AI工具)**
- text-generation (文本生成): chatbot, writing-assistant, content-creation
- image-generation (图像生成): ai-art, photo-enhancement, image-editing
- code-assistant (代码助手): code-completion, code-review, bug-detection
- data-analysis (数据分析): data-mining, predictive-analysis, pattern-recognition

**other (其他工具)**
- utility (实用工具): calculator, unit-converter, qr-generator
- generator (生成工具): text-generator, image-generator, data-generator
- testing (测试工具): website-test, performance-test, compatibility-test

## 严格分析要求：

1. **深度分析**：仔细阅读页面内容，识别网站的真实功能和定位
2. **准确命名**：使用网站的官方名称或最常用的中文名称
3. **详细描述**：描述必须具体、准确，包含主要功能和特色
4. **丰富标签**：标签必须相关、具体、有搜索价值，不能为空
5. **精确分类**：优先选择具体功能分类，避免使用"其他"类别
6. **标准格式**：严格按照JSON格式返回，不要添加额外说明

## 特别注意：
- 如果是计算器网站，应该分类到 other > utility > calculator
- 如果是在线工具集合，根据主要功能分类
- 如果是参考查询类网站，分类到 other > reference > 具体类型
- 标签不能为空，必须包含4-8个相关标签
- 描述不能少于80字，要详细说明功能和特点

请返回标准JSON格式，不要添加任何解释文字：
{
  "name": "网站的准确中文名称",
  "description": "80-150字的详细功能描述，包含主要功能、特色功能、适用场景等",
  "tags": ["功能标签", "行业标签", "技术标签", "用途标签", "特色标签"],
  "category": "一级分类ID",
  "subcategory": "二级分类ID",
  "subsubcategory": "三级分类ID"
}`

    const userPrompt = `请仔细分析以下网站，提供详细准确的分析结果：

网站URL: ${url}

页面内容片段:
${snippet.substring(0, 3000)}

请基于以上信息，严格按照要求分析网站功能，生成准确的名称、详细的描述、丰富的标签和精确的分类。特别注意：
1. 名称要准确反映网站身份
2. 描述要详细具体，不少于80字
3. 标签要相关且丰富，4-8个
4. 分类要精确，避免选择"其他"
5. 只返回JSON，不要其他文字`

    // Call AI API with monitoring
    const startTime = Date.now()
    let success = false
    let errorMessage: string | undefined
    let inputTokens = 0
    let outputTokens = 0
    let text = ''

    try {
      const provider = AIServiceFactory.getProvider()
      const messages: AIMessage[] = [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ]

      const aiResponse = await provider.callAPI(messages, {
        temperature: 0.3,
        max_tokens: 1500,
        stream: false
      })

      text = aiResponse.content || ''

      // 提取token使用信息
      if (aiResponse.usage) {
        inputTokens = aiResponse.usage.prompt_tokens || 0
        outputTokens = aiResponse.usage.completion_tokens || 0
      } else {
        // 如果没有usage信息，进行估算
        inputTokens = Math.ceil((systemPrompt.length + userPrompt.length) / 4)
        outputTokens = Math.ceil(text.length / 4)
      }

      success = true
    } catch (error) {
      errorMessage = error instanceof Error ? error.message : String(error)
      throw error
    } finally {
      // 记录AI使用情况
      const responseTime = Date.now() - startTime
      const modelName = AIServiceFactory.getCurrentProviderType() === 'glm' ? 'glm-4.5-air' : 'deepseek-chat'

      Promise.resolve().then(() => {
        AIUsageMonitor.logUsage({
          apiType: 'analyze-url',
          model: modelName,
          inputTokens,
          outputTokens,
          responseTimeMs: responseTime,
          success,
          errorMessage,
          requestId: `analyze-${url}`,
          userQuery: userPrompt.substring(0, 200)
        }).catch(err => {
          console.error('记录 AI 使用情况失败:', err)
        })
      })
    }

    // Step 3: Parse the AI's response
    let parsedResponse: any
    try {
      // Try to extract JSON from the response if it's wrapped in markdown or other text
      let jsonText = text.trim()

      // Remove markdown code blocks if present
      if (jsonText.includes('```json')) {
        const jsonMatch = jsonText.match(/```json\s*([\s\S]*?)\s*```/)
        if (jsonMatch) {
          jsonText = jsonMatch[1].trim()
        }
      } else if (jsonText.includes('```')) {
        const jsonMatch = jsonText.match(/```\s*([\s\S]*?)\s*```/)
        if (jsonMatch) {
          jsonText = jsonMatch[1].trim()
        }
      }

      // Find JSON object in the text
      const jsonMatch = jsonText.match(/\{[\s\S]*\}/)
      if (jsonMatch) {
        jsonText = jsonMatch[0]
      }

      parsedResponse = JSON.parse(jsonText)
    } catch (parseError) {
      console.error("Failed to parse AI response as JSON:", parseError)
      console.error("AI raw response:", text)

      // Try to extract information manually as fallback
      const fallbackName = extractFromText(text, ['name', '名称', 'title']) || extractNameFromUrl(url)
      const fallbackDescription = extractFromText(text, ['description', '描述', 'desc']) || `${fallbackName}是一个实用的在线工具`

      return NextResponse.json({
        name: fallbackName,
        description: fallbackDescription,
        tags: extractTagsFromText(text, url),
        category: "other",
        subcategory: "utility",
        subsubcategory: "calculator",
        error: "AI response parsing failed, using fallback analysis"
      })
    }

    // Validate and sanitize the parsed response
    const validatedResponse = {
      name: parsedResponse.name || extractNameFromUrl(url),
      description: parsedResponse.description || `${parsedResponse.name || '工具'}的在线服务平台`,
      tags: Array.isArray(parsedResponse.tags) ? parsedResponse.tags.slice(0, 6) : extractTagsFromUrl(url),
      category: parsedResponse.category || "other",
      subcategory: parsedResponse.subcategory || "utility",
      subsubcategory: parsedResponse.subsubcategory || "calculator",
    }

    return NextResponse.json(validatedResponse)
  } catch (error) {
    console.error("Error in analyze-url API:", error)
    return NextResponse.json(
      {
        error: "Internal server error during URL analysis",
        details: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}

// Helper functions for fallback analysis
function extractFromText(text: string, keywords: string[]): string {
  for (const keyword of keywords) {
    const regex = new RegExp(`${keyword}[："：]?\\s*["']?([^"'\\n,，。]{1,50})`, 'i')
    const match = text.match(regex)
    if (match && match[1]) {
      return match[1].trim()
    }
  }
  return ''
}

function extractNameFromUrl(url: string): string {
  try {
    const domain = new URL(url).hostname.replace('www.', '')
    const parts = domain.split('.')
    if (parts.length >= 2) {
      const name = parts[0]
      // Common domain to name mappings
      const nameMap: { [key: string]: string } = {
        'qq': '腾讯',
        'baidu': '百度',
        'taobao': '淘宝',
        'tmall': '天猫',
        'jd': '京东',
        'weibo': '微博',
        'zhihu': '知乎',
        'bilibili': '哔哩哔哩',
        'douyin': '抖音',
        'tiktok': 'TikTok',
        'youtube': 'YouTube',
        'github': 'GitHub',
        'google': 'Google',
        'microsoft': 'Microsoft',
        'apple': 'Apple',
        'amazon': 'Amazon',
        'netflix': 'Netflix'
      }
      return nameMap[name] || name.charAt(0).toUpperCase() + name.slice(1)
    }
    return domain
  } catch {
    return '未知工具'
  }
}

function extractTagsFromText(text: string, url: string): string[] {
  const tags: string[] = []
  try {
    const domain = new URL(url).hostname.toLowerCase()

    // Add domain-based tags
    if (domain.includes('video') || domain.includes('tv')) tags.push('视频')
    if (domain.includes('music')) tags.push('音乐')
    if (domain.includes('game')) tags.push('游戏')
    if (domain.includes('shop') || domain.includes('store')) tags.push('购物')
    if (domain.includes('news')) tags.push('新闻')
    if (domain.includes('edu')) tags.push('教育')
  } catch {
    // Ignore URL parsing errors
  }

  return tags.slice(0, 3)
}

function extractTagsFromUrl(url: string): string[] {
  return extractTagsFromText('', url)
}
